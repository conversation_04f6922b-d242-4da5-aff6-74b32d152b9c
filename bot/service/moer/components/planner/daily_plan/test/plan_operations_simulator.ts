import { PlanOperations } from '../../types'

/**
 * 任务项接口
 */
export interface TaskItem {
  id: string
  description: string
  time: string
  type?: 'fixed_time' | 'flexible'
}

/**
 * 计划操作模拟器
 * 用于展示计划操作前后的对比，不实际操作数据库
 */
export class PlanOperationsSimulator {

  /**
   * 模拟执行计划操作，返回操作前后的任务列表对比
   * @param originalTasks 原始任务列表
   * @param planOperations 计划操作
   * @returns 操作前后的对比结果
   */
  public static simulateOperations(
    originalTasks: TaskItem[],
    planOperations: PlanOperations
  ): {
    before: TaskItem[]
    after: TaskItem[]
    operations: {
      added: TaskItem[]
      updated: Array<{ before: TaskItem, after: TaskItem }>
      removed: TaskItem[]
      merged: Array<{
        from: TaskItem[],
        into: TaskItem,
        result: TaskItem
      }>
    }
  } {
    // 深拷贝原始任务列表
    let resultTasks = JSON.parse(JSON.stringify(originalTasks)) as TaskItem[]

    // 记录操作详情
    const operations = {
      added: [] as TaskItem[],
      updated: [] as Array<{ before: TaskItem, after: TaskItem }>,
      removed: [] as TaskItem[],
      merged: [] as Array<{
        from: TaskItem[],
        into: TaskItem,
        result: TaskItem
      }>
    }

    // 1. 处理合并操作 (需要先处理，因为会影响其他操作的ID映射)
    if (planOperations.toMerge && planOperations.toMerge.length > 0) {
      for (const mergeOp of planOperations.toMerge) {
        const { from, into, mergedContent } = mergeOp

        // 找到要合并的任务
        const fromTasks = from.map((id) => {
          const taskIndex = parseInt(id, 10) - 1
          return resultTasks[taskIndex]
        }).filter((task) => task)

        // 找到目标任务
        const intoIndex = parseInt(into, 10) - 1
        const intoTask = resultTasks[intoIndex]

        if (intoTask && fromTasks.length > 0) {
          // 记录合并操作
          const originalIntoTask = JSON.parse(JSON.stringify(intoTask))

          // 更新目标任务内容
          intoTask.description = mergedContent
          if (mergeOp.type) {
            intoTask.type = mergeOp.type
          }

          operations.merged.push({
            from: fromTasks,
            into: originalIntoTask,
            result: JSON.parse(JSON.stringify(intoTask))
          })

          // 标记被合并的任务为删除（除了目标任务）
          from.forEach((id) => {
            if (id !== into) {
              const taskIndex = parseInt(id, 10) - 1
              if (resultTasks[taskIndex]) {
                operations.removed.push(JSON.parse(JSON.stringify(resultTasks[taskIndex])))
                resultTasks[taskIndex] = null as any // 标记为删除
              }
            }
          })
        }
      }

      // 移除被标记删除的任务
      resultTasks = resultTasks.filter((task) => task !== null)
      // 重新分配ID
      resultTasks = resultTasks.map((task, index) => ({
        ...task,
        id: `${index + 1}`
      }))
    }

    // 2. 处理删除操作
    if (planOperations.toRemove && planOperations.toRemove.length > 0) {
      // 按ID降序排序，避免删除时索引变化的问题
      const sortedRemoveIds = planOperations.toRemove
        .map((id) => parseInt(id, 10))
        .sort((a, b) => b - a)

      for (const removeId of sortedRemoveIds) {
        const taskIndex = removeId - 1
        if (taskIndex >= 0 && taskIndex < resultTasks.length) {
          operations.removed.push(JSON.parse(JSON.stringify(resultTasks[taskIndex])))
          resultTasks.splice(taskIndex, 1)
        }
      }

      // 重新分配ID
      resultTasks = resultTasks.map((task, index) => ({
        ...task,
        id: `${index + 1}`
      }))
    }

    // 3. 处理更新操作
    if (planOperations.toUpdate && planOperations.toUpdate.length > 0) {
      for (const updateItem of planOperations.toUpdate) {
        const taskIndex = parseInt(updateItem.id, 10) - 1
        if (taskIndex >= 0 && taskIndex < resultTasks.length) {
          const originalTask = JSON.parse(JSON.stringify(resultTasks[taskIndex]))

          resultTasks[taskIndex].description = updateItem.content
          if (updateItem.type) {
            resultTasks[taskIndex].type = updateItem.type
          }

          operations.updated.push({
            before: originalTask,
            after: JSON.parse(JSON.stringify(resultTasks[taskIndex]))
          })
        }
      }
    }

    // 4. 处理新增操作
    if (planOperations.toAdd && planOperations.toAdd.length > 0) {
      const toAddArray = Array.isArray(planOperations.toAdd) ? planOperations.toAdd : []

      for (const addItem of toAddArray) {
        let newTask: TaskItem

        if (typeof addItem === 'string') {
          newTask = {
            id: `${resultTasks.length + 1}`,
            description: addItem,
            time: 'TBD',
            type: 'flexible'
          }
        } else {
          newTask = {
            id: `${resultTasks.length + 1}`,
            description: addItem.content,
            time: addItem.send_time,
            type: addItem.type
          }
        }

        resultTasks.push(newTask)
        operations.added.push(newTask)
      }
    }

    return {
      before: originalTasks,
      after: resultTasks,
      operations
    }
  }

  /**
   * 格式化输出对比结果
   * @param result 模拟结果
   * @returns 格式化的字符串
   */
  public static formatComparisonResult(result: ReturnType<typeof PlanOperationsSimulator.simulateOperations>): string {
    let output = ''

    output += '=== 计划操作前后对比 ===\n\n'

    // 原始计划
    output += '📋 **操作前的任务列表:**\n'
    result.before.forEach((task) => {
      output += `${task.id}. ${task.description} (${task.time})\n`
    })

    output += '\n'

    // 操作详情
    output += '🔄 **执行的操作:**\n'

    if (result.operations.added.length > 0) {
      output += '\n➕ **新增任务:**\n'
      result.operations.added.forEach((task) => {
        output += `  + ${task.description} (${task.time}) [${task.type}]\n`
      })
    }

    if (result.operations.updated.length > 0) {
      output += '\n✏️ **更新任务:**\n'
      result.operations.updated.forEach((update) => {
        output += `  📝 ID ${update.before.id}:\n`
        output += `     原内容: ${update.before.description}\n`
        output += `     新内容: ${update.after.description}\n`
      })
    }

    if (result.operations.merged.length > 0) {
      output += '\n🔗 **合并任务:**\n'
      result.operations.merged.forEach((merge) => {
        output += `  🎯 合并到 ID ${merge.into.id}:\n`
        output += `     被合并: ${merge.from.map((t) => `ID ${t.id}`).join(', ')}\n`
        output += `     原内容: ${merge.into.description}\n`
        output += `     新内容: ${merge.result.description}\n`
      })
    }

    if (result.operations.removed.length > 0) {
      output += '\n❌ **删除任务:**\n'
      result.operations.removed.forEach((task) => {
        output += `  - ID ${task.id}: ${task.description}\n`
      })
    }

    output += '\n'

    // 最终结果
    output += '📋 **操作后的任务列表:**\n'
    result.after.forEach((task) => {
      output += `${task.id}. ${task.description} (${task.time})\n`
    })

    output += '\n'
    output += `📊 **统计:** 原有 ${result.before.length} 个任务 → 最终 ${result.after.length} 个任务\n`

    return output
  }
}
