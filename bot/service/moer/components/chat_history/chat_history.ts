import { Config } from '../../../../config/config'
import { AIMessage as BotMessage, BaseMessage, HumanMessage } from '@langchain/core/messages'
import chalk from 'chalk'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import logger from '../../../../model/logger/logger'
import { catchError } from '../../../../lib/error/catchError'
import { DateHelper } from '../../../../lib/date/date'
import { ChatStateStore, IChatState } from '../../storage/chat_state_store'
import { ChatInterruptHandler } from '../message/interrupt_handler'

export interface IDBBotMessageOptions {
  is_send_by_human?: boolean // 人工回复
  round_id?: string   // LLM 输出的
   chat_state?: IChatState  //聊天时的客户状态
  is_recalled?: boolean // 消息被撤回
  message_id?: string // 消息 ID
  sop_id?:string
}

export interface IDBBaseMessage extends IBaseMessage, IDBBotMessageOptions {
    id: string
    created_at: Date
    chat_id: string
    short_description?: string // SOP 话术
}

export interface IBaseMessage {
    role: 'user' | 'assistant'
    content: string
}

export class ChatHistoryService {
  static archiveFlag = '[archived]'

  public static async getChatHistoryByChatId(chat_id: string, no_compress = false) {
    try {
      let chatHistory = await PrismaMongoClient.getInstance().chat_history.findMany({
        where: {
          chat_id: chat_id
        },
        orderBy: {
          created_at: 'asc'
        },
        select: {
          id: true,
          role: true,
          content: true,
          created_at: true,
          chat_id: true,
          short_description: true,
          is_send_by_human: true,
          round_id: true,
          is_recalled: true,
          message_id: true
        }
      }) as IDBBaseMessage[]

      // 如果有 archive flag，取 archive flag 往后的记录
      let latestArchiveIndex = -1

      for (let i = chatHistory.length - 1; i >= 0; i--) {
        if (chatHistory[i].role === 'assistant' && chatHistory[i].content === ChatHistoryService.archiveFlag) {
          latestArchiveIndex = i
          break
        }
      }

      if (latestArchiveIndex !== -1) {
        chatHistory = chatHistory.slice(latestArchiveIndex + 1)
      }

      if (!no_compress) {
        chatHistory = this.compressMarketingMessages(chatHistory)
      }

      return chatHistory
    } catch (error) {
      console.error('Error fetching chat records:', error)
      return []
    }
  }

  public static async getAllChatHistory() {
    try {
      let chatHistory = await PrismaMongoClient.getInstance().chat_history.findMany({
        orderBy: {
          created_at: 'asc'
        },
      }) as IDBBaseMessage[]
      chatHistory = this.compressMarketingMessages(chatHistory)
      return chatHistory
    } catch (error) {
      console.error('Error fetching chat records:', error)
      return []
    }
  }

  public static async getRecentLLMChatHistory(chat_id: string, rounds?: number): Promise<BaseMessage[]> {
    let chatHistory
    if (rounds === 0) {
      return []
    } else if (rounds) {
      chatHistory =  await this.getRecentConversations(chat_id, rounds, 'user')
    } else {
      chatHistory = await this.getChatHistoryByChatId(chat_id)
    }

    return chatHistory.map((message) => {
      if (message.role === 'user') {
        return new HumanMessage(message.content)
      } else {
        return new BotMessage(message.content)
      }
    })
  }



  public static async clearChatHistory(chat_id: string, archive = true) {
    try {
      // 假删除
      if (archive) {
        await this.addBotMessage(chat_id, ChatHistoryService.archiveFlag)
      } else {
        const result = await PrismaMongoClient.getInstance().chat_history.deleteMany({
          where: {
            chat_id
          },
        })
        logger.debug(chat_id, `${result.count} chat records with chatId '${chat_id}' have been cleared.`)
        return result.count
      }
    } catch (error) {
      console.error('Error clearing chat records:', error)
    }
  }


  public static async addBotMessage(chat_id: string, message: string, shortDes?: string, options?: IDBBotMessageOptions) {
    if (message) {
      await PrismaMongoClient.getInstance().chat_history.create({
        data: {
          chat_id: chat_id,
          role: 'assistant',
          content: message,
          created_at: new Date(),
          short_description: shortDes,
          is_send_by_human: options?.is_send_by_human,
          round_id: options?.round_id,
          chat_state: ChatStateStore.get(chat_id),
          is_recalled: options?.is_recalled,
          message_id: options?.message_id,
          sop_id: options?.sop_id
        }
      })
    }

    if (message !== ChatHistoryService.archiveFlag) {
      logger.log({ chat_id }, chalk.greenBright(`${Config.setting.BOT_NAME}: ${message}`))
    }
  }

  public static async addUserMessage(chat_id: string, message: string, roundId?:string) {
    // 移除 打卡模版 前缀
    message = message.replace(/[\s\S]*?(比如👉：)/, '').trim()

    if (message) {
      await PrismaMongoClient.getInstance().chat_history.create({
        data: {
          chat_id: chat_id,
          role: 'user',
          content: message,
          created_at: new Date(),
        }
      })
      logger.log({ chat_id }, chalk.blueBright(`客户: ${message}`))

      await ChatInterruptHandler.incrementChatVersion(chat_id)
    }
  }

  public static async getFormatChatHistoryByChatId(chat_id: string) {
    if (!chat_id) {
      return ''
    }

    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.BOT_NAME}: ${message.content.replaceAll('{', '').replaceAll('}', '')}`
    })
    return formattedHistory.join('\n')
  }

  public static async formatHistoryOnRole(chat_id: string, role: 'user' | 'assistant', last_rounds?: number) {
    let chatHistory = await this.getChatHistoryByChatId(chat_id)
    chatHistory = chatHistory.filter((message) => message.role === role)

    if (last_rounds) {
      chatHistory = chatHistory.slice(-last_rounds)
    }

    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.BOT_NAME}: ${message.content}`
    })

    return formattedHistory.join('\n')
  }

  public static formatHistoryHelper(messages: (IDBBaseMessage | BaseMessage)[]) {
    const formatContent = (content: string) => {
      return content
        .replace(/[{}]/g, '')
        .replace(/\n+/g, '\n')
        .trim()
    }

    return messages.map((message) => {
      if (message instanceof BaseMessage) {
        const role = message.getType() === 'human' ? '- 客户' : `  - ${Config.setting.BOT_NAME}`
        return `${role}：${formatContent(message.content as string)}`
      } else {
        const role = message.role === 'user' ? '- 客户' : `  - ${Config.setting.BOT_NAME}`
        return `${role}：${formatContent(message.content as string)}`
      }
    }).join('\n').trim()
  }

  static async repeatLastMessage(chat_id: string, s: string): Promise<boolean> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    if (chatHistory.length === 0) {
      return false
    }

    const lastMessage = chatHistory[chatHistory.length - 1]
    return lastMessage.content === s
  }

  static async setChatHistory(chat_id: string, chatHistory: IBaseMessage[]) {
    await this.clearChatHistory(chat_id)

    await PrismaMongoClient.getInstance().chat_history.createMany({
      data: chatHistory.map((message: IBaseMessage) => {
        return {
          chat_id: chat_id,
          role: message.role,
          content: message.content,
          created_at: new Date()
        }
      })
    })
  }

  static async getUserMessageCount(chat_id: string): Promise<number> {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    return chatHistory.filter((message) => message.role === 'user').length
  }

  static async getLastRoundHistory(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    let res: IDBBaseMessage[] = []

    // 倒序获取一轮对话
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user') {
        res = chatHistory.slice(i, chatHistory.length)
        break
      }
    }

    return res
  }

  public static async getRecentConversations (chat_id: string, rounds: number, role: 'user' | 'assistant' = 'user'): Promise<IDBBaseMessage []> {
    const chatHistory = await this.getChatHistoryByChatId (chat_id)
    let res: IDBBaseMessage [] = []
    let roundCount = 0

    // 从后向前遍历
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      // 如果是客户消息，增加轮次计数
      if (chatHistory [i].role === role) {
        roundCount++

        // 如果达到指定轮次数，结束遍历
        if (roundCount === rounds) {
          res = chatHistory.slice (i)
          break
        }
      }

      // 如果遍历到开头仍未达到指定轮次，返回全部历史
      if (i === 0) {
        res = chatHistory.slice ()
      }
    }

    return res
  }

  public static async getRecentConversationsAndStartWithAi (chat_id: string, rounds: number, role: 'user' | 'assistant' = 'user'): Promise<IDBBaseMessage []> {
    const chatHistory = await this.getChatHistoryByChatId (chat_id)
    let res: IDBBaseMessage [] = []
    let roundCount = 0

    // 从后向前遍历
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      // 如果是客户消息，增加轮次计数
      if (chatHistory [i].role === role) {
        roundCount++

        // 如果达到指定轮次数，结束遍历
        if (roundCount === rounds && chatHistory[i].role == 'assistant') {
          res = chatHistory.slice (i)
          break
        }
      }

      // 如果遍历到开头仍未达到指定轮次，返回全部历史
      if (i === 0) {
        res = chatHistory.slice ()
      }
    }

    return res
  }

  static async getLastAIMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'assistant') {
        return chatHistory[i].content
      }
    }
    return ''
  }

  static async getLastUserMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user') {
        return chatHistory[i].content
      }
    }
    return ''
  }

  static async countRemainingMsgAfterMsg(chat_id: string, message: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)
    const msgIndex = chatHistory.findIndex((msg) => msg.content === message)

    if (msgIndex === -1) {
      return -1
    }

    return chatHistory.length - msgIndex - 1
  }

  static async getLastMessage(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    return chatHistory[chatHistory.length - 1]
  }

  static async getUserMessages(chat_id: string, round?: number) {
    if (round) {
      const chatHistory = await this.getRecentConversations(chat_id, round, 'user')
      return chatHistory.filter((message) => message.role === 'user').map((message) => message.content)
    } else {
      const chatHistory = await this.getChatHistoryByChatId(chat_id)

      return chatHistory.filter((message) => message.role === 'user').map((message) => message.content)
    }
  }

  static async getBotMessages(chat_id: string) {
    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    return chatHistory.filter((message) => message.role === 'assistant').map((message) => message.content)
  }

  static async isRepeatedMsg(chat_id: string, message: string) {
    const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant', 5)

    // 分句检查是否重复
    // Split the sentence into clauses based on punctuation marks like period, comma, semicolon, etc.
    const clauses = message
      .split(/[,;!?，。；！？]/)
      .map((clause) => clause.trim())
      .filter((clause) => clause.replace(/[/#!$%^&*;:{}=\-_`~()？?，。！!、：；‘’“”（）《》【】[\]【】——…]/g, '').length >= 5)

    const whiteListContents = ['提升专注力', '先看小讲堂', 'https']

    // 尝试从分句中查找重复的部分
    const repeatedClause = clauses.find((clause) => chatHistory.includes(clause) &&
      !whiteListContents.some((content) => clause.includes(content))
    )

    let repeated = false
    let repeatedPart = ''
    let previousSentence = ''

    if (repeatedClause) {
      repeated = true
      repeatedPart = repeatedClause
      // 将 chatHistory 按换行分割，并查找包含重复部分的那句话
      const sentences = chatHistory.split('\n')
      previousSentence = sentences.find((sentence) => sentence.includes(repeatedClause)) || ''
    } else if (chatHistory.includes(message) && message.length >= 7) {
      repeated = true
      repeatedPart = message
      const sentences = chatHistory.split('\n')
      previousSentence = sentences.find((sentence) => sentence.includes(message)) || ''
    }

    // 如果检测到重复，则通过 logger.log 输出当前 message、重复的历史内容及重复部分
    if (repeated) {
      logger.log({ chat_id }, `重复检测 - 当前 message: ${message}`)
      logger.log({ chat_id }, `重复检测 - 与之前重复的那句话: ${previousSentence}`)
      logger.log({ chat_id }, `重复检测 - 重复的部分: ${repeatedPart}`)
    }

    return repeated
  }

  static async hasRepeatedMsg(chat_id: string, toMatch: string) {
    const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant')

    return chatHistory.includes(toMatch)
  }

  private static compressMarketingMessages(chatHistory: IDBBaseMessage[]) {
    const totalMessages = chatHistory.length
    return chatHistory.map((message, index) => {
      if (index >= totalMessages - 10 || !message.short_description) {
        return message
      }
      return { ...message, content: message.short_description }
    })
  }

  /**
   * 将指定 消息放到最后
   * @param chat_id
   * @param userMessage
   */
  static async moveToEnd(chat_id: string, userMessage: string) {
    if (!userMessage) {
      return
    }

    const chatHistory = await this.getChatHistoryByChatId(chat_id)

    let message: IDBBaseMessage | null = null
    for (let i = chatHistory.length - 1; i >= 0; i--) { // 有可能客户会重复说同一句话，倒序遍历会更稳定一些
      if (chatHistory[i].content === userMessage) {
        message = chatHistory[i]
        break
      }
    }
    if (!message) {
      return
    }

    await catchError(PrismaMongoClient.getInstance().chat_history.update({
      where: {
        id: message.id
      },
      data: {
        created_at: new Date()
      }
    }))
  }

  /**
   @return 是否在 duration 的时间范围内有非营销的聊天记录
   * @param chatId
   * @param duration
   * @param unit
   */
  static async isLastMessageWithDuration(chatId: string, duration:number, unit: 'second' | 'minute') {
    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

    if (chatHistory.length < 1) return false

    const lastMsg = chatHistory[chatHistory.length - 1]
    if (lastMsg.short_description) return false

    const lastMsgSendTime = lastMsg.created_at
    return DateHelper.diff(lastMsgSendTime, new Date(), unit) < duration
  }

  static async getMessageByMessageId(externalId: string) {
    return PrismaMongoClient.getInstance().chat_history.findFirst({
      where: {
        message_id: externalId
      }
    })
  }

  static async updateMessageId(id: string, messageId: string) {
    return PrismaMongoClient.getInstance().chat_history.update({
      where: {
        id
      },
      data: {
        message_id: messageId
      }
    })
  }

  static async setMessageRecalled(messageId: string) {
    const message = await this.getMessageByMessageId(messageId)

    if (!message) return

    await catchError(PrismaMongoClient.getInstance().chat_history.update({
      where: {
        id: message.id
      },
      data: {
        is_recalled: true
      }
    }))
  }

  /**
   * 判断是否在 startTime 之后有对话
   * @param chatId
   * @param startTime
   */
  public static async hasConversationRecently(chatId: string, startTime: Date):Promise<boolean> {
    const conversationCount = await PrismaMongoClient.getInstance().chat_history.count({
      where: {
        chat_id:chatId,
        role: 'user',
        created_at:{
          gte: startTime
        }
      }
    })
    return conversationCount > 0
  }

  /**
   * 根据日期和 chat_id 获取该日期的第一条消息的用户画像
   * @param chatId 聊天ID
   * @param targetDate 目标日期 (YYYY-MM-DD 格式)
   * @returns 用户画像的 JSON 字符串
   */
  public static async getCustomerPortraitByDate(chatId: string, targetDate: string): Promise<Record<any, any>> {
    try {
      // 解析目标日期
      const date = new Date(targetDate)
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0)
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59)

      // 查询该日期的第一条消息
      const firstMessage = await PrismaMongoClient.getInstance().chat_history.findFirst({
        where: {
          chat_id: chatId,
          created_at: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        orderBy: {
          created_at: 'asc'
        },
        select: {
          id: true,
          chat_state: true,
          created_at: true,
          content: true
        }
      })

      if (!firstMessage) {
        return {}
      }

      if (!firstMessage.chat_state) {
        return {}
      }

      // 提取 moerUserSlots
      const chatState = firstMessage.chat_state as any
      const moerUserSlots = chatState.moreUserSlots || {}

      return moerUserSlots
    } catch (error) {
      logger.error('获取用户画像失败:', error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      return {}
    }
  }
}