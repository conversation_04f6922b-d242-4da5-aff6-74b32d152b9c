import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { JuziAPI } from '../../../bot/lib/juzi/api'
import { MoerAPI } from '../../../bot/model/moer_api/moer'
import { IWecomMsgType } from '../../../bot/lib/juzi/type'
import axios from 'axios'

describe('Test', function () {
  beforeAll(() => {

  })

  it('获取群信息，用于配置通知群，以及人工介入群', async () => {
    //     '695': '1688857949631398', // 彤彤
    //     '669': '1688857003605938', // 乔乔
    //     '775': '1688856322643146', // 墨尔3
    //     '791': '1688854822630695', // 墨尔4
    //     '769': '1688855842687484', // 墨尔5
    //     '753': '1688855025632783', // 冥想6
    //     '754': '1688858047620029', // 冥想7
    //     '837': '1688855739679096', // 麦子 9
    //     '838': '1688855184697783', // 麦子 10
    //     '840': '1688855548631328', // 麦子 11
    //     '836': '1688855587643034', // 麦子 12
    //     '764': '1688856297674847', // 麦子 13
    //     '844': '1688855184697783' // 麦子 15
    // 1688857443695295 baoshu1
    // 1688854494648005 baoshu2
    // 1688857443696504

    const ids = ['1688857443695295', '1688854494648005', '1688857443696504']
    const counselors = [
      'ZhuYangYang',
      'QueBi',
      'ZhangZiHao',
      'ZhaiShuai',
      'HuRong_1',
      'GaoTianJiao',
      'WeiQingPing'
    ]

    // 企微客户，去重
    const counselorImContactIdMap = {}

    for (const id of ids) {
      const rooms =  await JuziAPI.listGroup(id)

      for (const room of rooms) {
        // console.log(room.name, room.imRoomId, room.owner === id, room.owner)
        for (const member of room.memberList) {
          if (counselors.includes(member.externalUserId)) {
            counselorImContactIdMap[member.externalUserId] = member.imContactId
          }
        }
      }

      console.log(JSON.stringify(counselorImContactIdMap, null, 4))
    }
  }, 60000)

  it('暴叔账号测试下', async () => {
    const configs = [
      {
        name: 'baoshu7',
        wechatId: '1688854493523277',
        port: '3009',
        botUserId: 'BaoShu_1',
        notifyGroupId: 'R:10969557259281977',
        orgToken: '661cfbe507ea8c7bacfe5c8c',
        counselors: ['HuRong_1', 'GaoTianJiao'],
        xbbId: ''
      },
      {
        name: 'baoshu8',
        wechatId: '1688855043510357',
        port: '3010',
        botUserId: 'BaoShu',
        notifyGroupId: 'R:10840704064745792',
        orgToken: '661cfbe507ea8c7bacfe5c8c',
        counselors: ['HuRong_1', 'GaoTianJiao'],
        xbbId: ''
      }
    ]

    for (const config of configs) {
      // 定义需要插入的数据
      const configData = generateConfigDataForBaoshu(config)

      // 调用 PrismaMongoClient 插入新配置
      await PrismaMongoClient.getConfigInstance().config.create({
        data: configData,
      })
    }

    await clearServerCache()
  }, 60000)

  it('缓存清除', async () => {
    await clearServerCache()
  }, 30000)

  it('添加新的账号配置', async () => {
    // 张雪媛 67a9aa1e1c67222872f5d9cd
    // MaiZi01(1688854406714261)

    const configs = [
      {
        name: 'moer20',
        wechatId: '1688854406714261',
        port: '4021',
        botUserId: 'MaiZi01',
        notifyGroupId: 'R:10835916603787626',
        classGroupId: 'R:10933504297161826',
        orgToken: '67ab2594e8ae205e9299c8d3',
      }
    ]

    for (const config of configs) {
      // 定义需要插入的数据
      const configData = generateConfigDataForMoer(config)

      // 调用 PrismaMongoClient 插入新配置
      await PrismaMongoClient.getConfigInstance().config.create({
        data: configData,
      })

      // 生成 docker-compose.yaml 配置
      console.log(generateDockerConfig(config))

      // 使用账号在通知群里发一条消息
      await JuziAPI.sendGroupMsg(config.wechatId, config.notifyGroupId, {
        type: IWecomMsgType.Text,
        text: `测试 ${config.name} 账号添加`
      })
    }
  }, 60000)

  it('simpleId', async () => {
    console.log(JSON.stringify(await MoerAPI.getSimpleIdList(), null, 4))
  }, 60000)
})


function generateDockerConfig(config: any): string {
  return `${config.name}:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: ${config.name}
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=${config.name}
      - TZ=Asia/Shanghai
    ports:
      - "${config.port}:${config.port}"
    restart: always`
}


// 辅助函数：根据传入的参数生成配置数据
function generateConfigDataForBaoshu({
  name,
  wechatId,
  port,
  botUserId,
  notifyGroupId,
  counselors,
  xbbId,
}) {
  return {
    // 将 nickname 映射为 enterpriseName，同时生成 accountName（此处仅作示例，可自行调整）
    enterpriseName: 'baoshu',
    accountName: name,
    wechatId,
    port,
    // 根据传入的端口拼接地址，若有其它规则请自行调整
    address: `http://*************:${port}`,
    botUserId,
    orgToken: '661cfbe507ea8c7bacfe5c8c',
    enterpriseConfig: {
      notifyGroupId,
      counselors,
      xbbId
    },
    // 时间字段（根据需求可以改为 new Date() 表示当前时间）
    createdAt: new Date(),
    updatedAt: new Date(),
  }
}


// 辅助函数：根据传入的参数生成配置数据
function generateConfigDataForMoer({ name,
  wechatId,
  port,
  botUserId,
  notifyGroupId,
  classGroupId,
  orgToken,
  isGroupOwner = false
}) {
  return {
    // 将 nickname 映射为 enterpriseName，同时生成 accountName（此处仅作示例，可自行调整）
    enterpriseName: 'moer',
    accountName: name,
    wechatId,
    port,
    // 根据传入的端口拼接地址，若有其它规则请自行调整
    address: `http://***************:${port}`,
    botUserId,
    orgToken,
    enterpriseConfig: {
      notifyGroupId,
      classGroupId,
      isGroupOwner
    },
    // 时间字段（根据需求可以改为 new Date() 表示当前时间）
    createdAt: new Date(),
    updatedAt: new Date(),
  }
}

async function clearServerCache() {
  const res = await axios.post('http://*************:6001/api/clear-server-address-cache')
  console.log(JSON.stringify(res.data, null, 4))
}