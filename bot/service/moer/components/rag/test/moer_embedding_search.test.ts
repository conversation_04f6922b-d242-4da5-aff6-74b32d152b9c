import ElasticSearchService, { ElasticSearchClient } from '../../../../../model/elastic_search/elastic_search'
import { MoerRag } from '../moer_embedding_search'
import { AzureOpenAIEmbedding } from '../../../../../lib/ai/llm/openai_embedding'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { UUID } from '../../../../../lib/uuid/uuid'
import { getChatId } from '../../../../../config/chat_id'
import { WorkFlow } from '../../flow/flow'
import { WealthKeyExtract } from '../../../prompt/moer/wealthKeysExtract'
import { QueryRewritePrompt } from '../../../prompt/moer/userQueryRewrite'
import Siliconflow from '../../../../../lib/sliconflow/siliconflow'
import { Config } from '../../../../../config/config'
import { MoerGeneralRAG } from '../moer_general'
import { WealthOrchardRag } from '../wealth_orchard'
import { RAGHelper } from '../../../../../model/rag/rag'

interface IElasticEmbeddingRes {
  pageContent: string
  metadata: Record<string, any>
  score: number
}
interface IEmbeddingSearchRes {
  rewriteQuestions: string
  question: string
  answer: string
  embeddingScore: number
  rerankScores: number
}


describe('moer_embedding_search', () => {
  it('testSearch', async () => {
    console.log(JSON.stringify(await MoerGeneralRAG.search('抖音支付怎么搞的', '7881303433021914_1688854822630695', UUID.v4()), null, 4))
  }, 60000)

  it('testRerank', async () => {
    const query = '客户完成第一课，表示感觉挺好，计划明天继续学习。'
    const QAs = [{ q:'客户反馈听完课程后感觉好，有效果，并且不停打哈欠。', a:'' }, { q:'客户表示第一课收获不多，沉浸式体验不理想，效果与平时在网易听的冥想音乐差不多。', a:'' }]

    console.log(await RAGHelper.reRank(query, QAs, { top_n: 10, threshold: 0.1 }))
  }, 60000)


  it('检查是否rag', async () => {
    const query = '👌'
    const isRagEmbeddingSearchRes = await MoerRag.isRagEmbeddingSearch(query)
    console.log('isRagEmbeddingSearchRes:', isRagEmbeddingSearchRes)

  }, 3E8)


  it('hierarchicalEmbeddingSearch测试', async () => {
    const queryRewriteResult = '作为初学者，我需要有什么准备才能参加冥想入门营？'
    const chatId = '186'
    const result = await MoerRag.hierarchicalEmbeddingSearch('moer_test', queryRewriteResult, chatId, {
      lowestScore: 0.8,
      subQueryOutputNumber: 10,
      outputNumber: 2,
      // currentDay: 1
    })
    console.log('hierarchicalEmbeddingSearch结果:', result)

  }, 30000)

  it('财富果园测试', async () => {
    const query = '我看到红色的大门和苹果'
    const prompt = await WealthKeyExtract.format(query)
    const subcategories = await WealthOrchardRag.wealthKeysExtract(query, prompt)
    console.log('Extracted subcategories:', subcategories)
    const combinedContent = await WealthOrchardRag.embeddingSearchWealth(subcategories)
    console.log('Combined content:', combinedContent)
    // Add assertions here if needed
  }, 30000)

  it('elastic embedding阈值', async () => {
    try {
      const query =  '我看到咱们还有红靴子，这是在什么时候上的？'
      const filter = {
        'bool': { 'should':[
          { 'term':{ 'metadata.doc':'第一天课程-情绪解压.docx' } },
          { 'term':{ 'metadata.doc':'第二天课程-财富果园.docx' } },
          { 'term':{ 'metadata.doc':'第三天课程-效能提升.docx' } },
          { 'term':{ 'metadata.doc':'冥想问题.xlsx' } },
          { 'term':{ 'metadata.doc':'常规问题全局.xlsx' } },
          { 'term':{ 'metadata.doc':'常规问题周三八点前.xlsx' } },
          { 'term':{ 'metadata.doc':'课后问题回访FAQ.docx' } },
          { 'term':{ 'metadata.doc':'系统班全通班逐字稿' } }

        ] } }
      const results = await ElasticSearchService.embeddingSearch('moer_rag_2_2048d', query, 10, 0.6, filter)
      console.log('results', results)
    } catch (error) {
      console.error('Error in embedding search:', error)
    }
  }, 30000)

  it('esSearch', async () => {
    const q = '冥想对我们的身心有什么好处？'

    const res = await ElasticSearchClient.getInstance().search({
      index: 'moer_rag_2_2048d',
      body: {
        knn: {
          field: 'embedding',
          query_vector: await AzureOpenAIEmbedding.getInstance().embedQuery(q),
          num_candidates: 50,
          k: 10,
          filter: {
            'bool': {
              'should': [
                { 'term': { 'metadata.doc': '第二天课程-财富果园.docx' } },
                { 'term': { 'metadata.doc': '第三天课程-效能提升.docx' } }
              ]
            }
          }
        }
      },
    })

    res.hits.hits.forEach((hit: any) => {
      console.log(JSON.stringify(hit._source.text, null, 4))
      console.log(JSON.stringify(hit._score, null, 4))
      console.log(JSON.stringify(hit._source.metadata.doc, null, 4))
    })

  }, 30000)

  it('asdasd', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getRecentLLMChatHistory('7881300846030208_1688854546332791', 0), null, 4))
  }, 60000)

  it('rag检查', async () => {
    const query =  '老师叫什么'
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    // await WorkFlow.step(chat_id, user_id, '我这些天生病了')
    // await WorkFlow.step(chat_id, user_id, '情绪不稳定，有天竟然把一个月的睡眠药一次性吃了')
    // await WorkFlow.step(chat_id, user_id, '系统班的时长是多久')
    const ragResults = await MoerRag.ragImplementation('moer_rag_2_2048d', query, chat_id)
    console.log('ragResults', ragResults)
  }, 1E8)

  it('embeddingSearch 检查', async () => {
    const query = '如果错过了这次优惠，以后还有机会参加吗？'
    const chatId = '124353'
    const chatHistory = ''
    const queryRewriteResults = await MoerRag.queryReWrite(query, await QueryRewritePrompt.format(query, chatHistory))
    console.log('Query Rewrite Results:', queryRewriteResults)
    const embeddingSearchResults: IEmbeddingSearchRes[] = []
    for (const queryRewriteResult of queryRewriteResults) {
      const results = await MoerRag.embeddingSearch('moer_rag_2_2048d', queryRewriteResult, chatId, {
        lowestScore: 0,
        subQueryOutputNumber: 10,
        outputNumber: 2
        // currentDay: 1
      })
      embeddingSearchResults.push(...results)
    }

    console.log('Embedding Search Results:', embeddingSearchResults)
  }, 3000000)


  it('queryRewrite检查', async () => {
    const query =  '我想要学会控制情绪。这个课可以让我控制情绪么？能治好阳痿么'
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '我这些天生病了')
    await WorkFlow.step(chat_id, user_id, '情绪不稳定，有天竟然把一个月的睡眠药一次性吃了')
    await WorkFlow.step(chat_id, user_id, '系统班的时长是多久')
    //await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    //await WorkFlow.step(chat_id, user_id, '稍等一下吧')
    const recentConversations = await ChatHistoryService.getRecentConversations(chat_id, 3)
    const chat_history = ChatHistoryService.formatHistoryHelper(recentConversations)
    const queryRewriteResults = await MoerRag.queryReWrite(query, await QueryRewritePrompt.format(query, chat_history))
    console.log('重写后的query', queryRewriteResults)
  }, 3000000)

  it('should return the correct result', async () => {
    const query =  '我们后期上课在哪里上'
    const chatId = '123'
    const chat_history:string = ''
    const queryRewriteResults = await MoerRag.queryReWrite(query, await QueryRewritePrompt.format(query, chat_history))

    console.log('重写后的query', queryRewriteResults)
    //query = await queryReWrite(query)

    const promises: Promise<any[]>[] = []

    for (const queryRewriteResult of queryRewriteResults) {
      promises.push(MoerRag.embeddingSearch('moer_rag', queryRewriteResult, chatId, {
        subQueryOutputNumber: 10,
        outputNumber: 3,
        lowestScore: 0.8
      }))
    }
    const res = await Promise.all(promises)
    console.log('res', res)
  }, 6000000)


  it('reRank test', async () => {
    const siliconFlow = new Siliconflow()

    siliconFlow.auth(Config.setting.siliconFlow.apiKey)


    const response = await siliconFlow.createRerank({
      model: 'BAAI/bge-reranker-v2-m3',
      query: 'Apple',
      documents: [
        '苹果',
        '香蕉',
        '水果',
        '蔬菜'
      ],
      top_n: 4
    })

    console.log(response)
  }, 60000)
})