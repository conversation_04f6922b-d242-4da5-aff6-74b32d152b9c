import { ChatStateStore, ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import axios from 'axios'
import { DataService } from '../bot/service/moer/getter/getData'
import { MoerAPI } from '../bot/model/moer_api/moer'
import { Config } from '../bot/config/config'
import { HandleEnergyTest } from '../bot/service/moer/components/flow/schedule/task/handleEnergyTest'
import { MoerNode } from '../bot/service/moer/components/flow/nodes/type'
import { getScript } from '../bot/service/moer/components/script/script'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { ChatDB } from '../bot/service/moer/database/chat'
import { RegexHelper } from '../bot/lib/regex/regex'
import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'
import ElasticSearchService from '../bot/model/elastic_search/elastic_search'

describe('Test', function () {
  beforeAll(() => {

  })

  it('1123123128899', async () => {
    const today = new Date()
    if (
      today.getFullYear() === 2025 &&
        today.getMonth() === 5 && // 月份从 0 开始，6 月是 5
        today.getDate() === 24
    ) {
      console.log('hi')
    }

  }, 30000)

  it('worker123', async () => {
    // `${Config.setting.wechatConfig?.id}_${Config.setting.wechatConfig?.classGroupId.replaceAll(':', '')}`
    const queueName = '1688856322643146_R10915716002894380'
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    console.log(JSON.stringify(await queue.getWorkers(), null, 4))

    // const completedJobs = await queue.getJobs(['completed'], 0, 4, false)
    // console.log(JSON.stringify(completedJobs, null, 2))


  }, 60000)

  it('test4ertfygh', async () => {
    const playBackTime = 2 * 60
    let isInClass

    if (new Date().getHours() === 20 && new Date().getMinutes() <= 50) {
      const courseStartTime = new Date().setHours(20, 0, 0, 0)
      const diffMillis =  Date.now() - courseStartTime
      const remainingMinutes = Math.floor(diffMillis / 60000)
      const thresholdMinutes = Math.floor(remainingMinutes / 10)

      isInClass = playBackTime >= thresholdMinutes * 60

      console.log(isInClass)
    }
  }, 60000)

  it('槽位处理', async () => {
    //     const llmResponse = `- 基本信息::年龄::36岁
    // - 基本信息::身高::1.85米
    // - 兴趣爱好::运动::打篮球
    // - 兴趣爱好::音乐::唱歌`
    //     const slotOriginList = llmResponse.split ('\n').map ((item) =>
    //       UserSlot.fromString (item)

    // await ExtractUserSlotsV2.extract('7881300846030208_1688854546332791', [{ role:'user', date:'2025-03-13', message:'我想给我14岁的女儿学习，她抑郁状态，你们有对应的服务么？' }], {})
  }, 60000)

  it('testme', async () => {
    const slotList = [
      {
        'topic': '基本信息',
        'subTopic': '年龄',
        'content': '36岁',
        'frequency': 1
      },
      {
        'topic': '基本信息',
        'subTopic': '身高',
        'content': '1.85米',
        'frequency': 1
      },
      {
        'topic': '兴趣爱好',
        'subTopic': '运动',
        'content': '打篮球',
        'frequency': 1
      },
      {
        'topic': '兴趣爱好',
        'subTopic': '音乐',
        'content': '唱歌',
        'frequency': 1
      }
    ]


  }, 60000)

  it('123asda', async () => {
    await ElasticSearchService.describeIndex('moer_rag_2')
  }, 60000)

  it('haha', async () => {
    await axios.get(`https://fwalert.com/d675887f-7151-4bb1-9da4-fdb7000c9c23?user=${encodeURIComponent('文都')}`)
  }, 60000)

  it('123123', async () => {
    const nodeInvokeCount = 1
    console.log(`${nodeInvokeCount < 1 ? '3. 询问客户解读是否有启示或者不明白的地方，并提供更深入的解释' : ''}`)
  }, 60000)

  it('获取任务', async () => {
    const queue = new Queue('7881299925165083_1688856322643146', {
      connection: RedisDB.getInstance()
    })

    console.log(await queue.count())
  }, 60000)

  it('lru test', async () => {
    await ChatStatStoreManager.initState('7881301047907394_1688854546332791')
    await ChatStatStoreManager.initState('7881301047907394_1688854546332791')
    // await sleep(2000)
    // await ChatStatStoreManager.initState('7881301047907394_1688854546332791')
  }, 60000)

  it('tasks', async () => {
    const chats = await DataService.getChatsByCourseNo(55)

    for (const chat of chats) {
      if (await DataService.isPaidSystemCourse(chat.id)) {
        continue
      }

      // // 添加任务
      // const userId = getUserId(chat.id)
      // await addTask(userId, chat.id)
    }

    console.log(chats.length)
  }, 60000)

  it('123adas', async () => {
    console.log(await DataService.isPaidSystemCourse('7881302054958141_1688857949631398'))
  }, 60000)

  it('123123ad', async () => {
    function splitSentence(text: string) {
      // 1. 先按照序号分割，例如 "1. "、"2. " 等
      const sections = text
        .split(/(?=\d+\.)(?!\d)/)

      let splitSentences: string[]
      if (sections.length > 1) {
        splitSentences = sections
      } else {
        // 2. 按标点分割成数组
        splitSentences = text
          .split(/(?<=[?!。！？\n])(?![”"'])/)
      }

      const rawSentences =  splitSentences.map((s) => s.trim())
        .filter(Boolean)

      // 2. 如果句子数 <=5，原样返回
      if (rawSentences.length < 5) {
        return rawSentences
      }

      // 3. 分句数 >=5 时，两两合并
      const combined: string[] = []
      for (let i = 0; i < rawSentences.length; i += 2) {
        // 取当前句子
        let merged = rawSentences[i]
        // 如果下一句存在，就合并
        if (rawSentences[i + 1]) {
          merged += ` ${rawSentences[i + 1]}`
        }
        combined.push(merged)
      }

      return combined
    }

    console.log(splitSentence('唐宁老师确实是个很特别的人呢～她不仅是我们的创始人，也是冥想的主讲老师。她的课程已经帮助了1267.2万人，还培育了600多位生命智慧的讲师呢！另外，她特别擅长“红靴子飞跃”这种冥想，听说徐峥老师都成了她的“冥想死忠粉”哦😄'))
  }, 60000)

  it('heart', async () => {
    let sentence = '❤️'

    const emoji = RegexHelper.extractEmoji(sentence)
    if (emoji) {
      sentence = sentence.replace(emoji, '') // emoji 去重
    }

    console.log(sentence.trim() === '')
  }, 60000)

  it('should extract emoji from sentence', () => {
    const sentence = 'Hello ❤️, how are you?'
    const emoji = RegexHelper.extractEmoji(sentence)
    expect(emoji).toBe('❤️')
  })

  it('should return null if no emoji is found', () => {
    const sentence = 'Hello, how are you?'
    const emoji = RegexHelper.extractEmoji(sentence)
    expect(emoji).toBeNull()
  })


  it('', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('123123sdas', async () => {
    const recordingLink = await DataService.getCourseLink(2, '7881301541987220_1688858047620029', true)
    console.log(recordingLink)
  }, 60000)
  it('123123', async () => {
    const a = [1, 2, 3]
    const b = [4, 5, 6]
    console.log([...a, ...b])
  }, 60000)

  it('moerId', async () => {
    const chat =  await ChatDB.getByMoerId('845931')
    console.log(JSON.stringify(chat?.id, null, 4))
  }, 60000)

  it('123123', async () => {
    const moerUser = await MoerAPI.getUserByPhone('13432132803')

    console.log(JSON.stringify(moerUser, null, 4))
  }, 60000)

  it('chatHistory', async () => {
    const chatHistory = await ChatHistoryService.getRecentLLMChatHistory('7881302369134376_1688857003605938', 0)

    console.log(chatHistory)
  }, 60000)

  it('asdas123312', async () => {
    const chatId = '7881300741986032_1688857003605938'
    const userMessages = (await ChatHistoryService.getUserMessages(chatId)).join('\n')

    console.log(userMessages)
  }, 60000)

  it('123123213', async () => {
    const day0Script = getScript().pre_course_day
    console.log(JSON.stringify(await day0Script.pre_course.content('7881300171918532_1688857003605938'), null, 4))
    console.log(JSON.stringify(await DataService.getCurrentTime('7881300171918532_1688857003605938'), null, 4))
  }, 60000)

  it('rag', async () => {
    // const ragInfo = await MoerGeneralRAG.search('小课堂完课礼是什么', UUID.short())
    // console.log(ragInfo)
  }, 60000)

  it('should pass', async () => {
    const url = 'www.bilibili.com/video/BV1234567890'

    // 定义用于匹配Bilibili视频链接的正则表达式
    const regex = /(?:https?:\/\/)?(?:www\.)?bilibili\.com\/video\/(?:av(\d+)\/)?(?:BV(\w+))/i
    // 尝试匹配给定的URL
    console.log(regex.test(url))
  })

  it('test', async () => {
    const userMessages =  [{ timestamp: 1234567890 }, { timestamp: 1 }]
    userMessages.sort((msgA, msgB) => {
      return msgA.timestamp - msgB.timestamp
    })

    console.log(userMessages)
  }, 30000)


  it('', async () => {
    const msg = '我是大二的学生'
    const welcomeMsgRegex = /^我是.{1,10}$/
    console.log(welcomeMsgRegex.test(msg))
  }, 60000)

  it('time compare', async () => {
    console.log(Number(new Date().getTime()) < new Date('2024-06-14T17:23:00+08:00').getTime())
  }, 60000)

  it('null', async () => {
    console.log(null as any instanceof Object)
  }, 60000)

  it('test', async () => {
    console.log(JSON.stringify(await axios.get('http://112.124.32.162:3001'), null, 4))
  }, 60000)

  it('123123123', async () => {
    const isCompleteClass = await DataService.isCompletedCourse('7881301588955686_1688857003605938', { day: 0 })
    console.log(isCompleteClass)
  }, 60000)

  it('clear', async () => {
    console.log(JSON.stringify(await MoerAPI.getCurrentCourseInfo(37), null, 4))
  }, 60000)

  it('eT', async () => {
    console.log(JSON.stringify(await DataService.getEnergyTestScore('7881299498968150_1688857003605938'), null, 4))
  }, 60000)

  it('EnergyTestAnalyze', async () => {
    Config.setting.localTest = true;
    (new HandleEnergyTest() as any).getAnalyze(74, '123', '7881299498968150_1688857003605938')
  }, 60000)

  it('fk u', async () => {
    const chat_id = '7881300846030208_1688854546332791'
    await ChatStatStoreManager.initState(chat_id)
    const chatState = ChatStateStore.get(chat_id)
    console.log(JSON.stringify(chatState, null, 4))
    ChatStateStore.update(chat_id, {
      nextStage: MoerNode.IntentionQueryBeforeClass,
    })
    console.log(JSON.stringify(ChatStateStore.get(chat_id), null, 4))
    await ChatStatStoreManager.initState(chat_id)

    console.log(JSON.stringify(ChatStateStore.get(chat_id), null, 4))
  }, 60000)

  it('asdasdaasd', async () => {
    const timeString = '01:27:05'
    function timeStringToSeconds(timeString: string): number {
      const timeParts = timeString.split(':')
      let seconds = 0

      if (timeParts.length === 3) {
        // 格式为 HH:MM:SS
        const hours = parseInt(timeParts[0], 10)
        const minutes = parseInt(timeParts[1], 10)
        const secs = parseInt(timeParts[2], 10)

        seconds = hours * 3600 + minutes * 60 + secs
      } else if (timeParts.length === 2) {
        // 格式为 MM:SS
        const minutes = parseInt(timeParts[0], 10)
        const secs = parseInt(timeParts[1], 10)

        seconds = minutes * 60 + secs
      } else if (timeParts.length === 1) {
        // 格式为 SS
        seconds = parseInt(timeParts[0], 10)
      } else {
        throw new Error('无效的时间格式')
      }

      return seconds
    }

    console.log(timeStringToSeconds(timeString))  // 输出：5225
  }, 60000)
})