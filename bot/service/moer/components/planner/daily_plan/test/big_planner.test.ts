import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { getPrompt } from '../../../agent/prompt'
import { BigPlanner } from '../big_planner'
import { DataService } from '../../../../getter/getData'
import { ContextBuilder } from '../../../agent/context'
import { taskTimeToCourseTime, IScheduleTime } from '../../../schedule/creat_schedule_task'
import dayjs from 'dayjs'
import { Planner } from '../../plan/planner'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'
import ElasticSearchService from '../../../../../../model/elastic_search/elastic_search'
import { MemoryRecall } from '../../../memory/memory_search'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import MockDate from 'mockdate'
import { UserSlots } from '../../../flow/helper/slotsExtract'
import { PlanOperationsSimulator } from './plan_operations_simulator'

/**
 * BigPlanner Mock 测试套件
 *
 * 这个测试套件用于模拟 BigPlanner 在不同时间点的规划生成，主要功能：
 * 1. Mock getCurrentTime 函数，模拟不同日期的时间
 * 2. Mock context 数据（客户画像、记忆、行为等）
 * 3. Mock 每天的 SOP 任务数据
 * 4. 循环测试从上课前两天到上课周结束的每一天
 *
 * 使用方法：
 * 1. 修改 prepareDailySOPMap() 函数，填入每天的 SOP 数据
 * 2. 修改 prepareCustomerPortrait()、prepareMemories() 等函数，填入客户数据
 * 3. 运行测试，查看每天生成的计划结果
 *
 * 注意：这是一个测试架子，用户需要根据实际需求填充具体的测试数据
 */
describe('BigPlanner Mock Tests', function () {
  let getCurrentTimeSpy: jest.SpyInstance
  let filterSOPByDateSpy: jest.SpyInstance
  let getCustomerPortraitSpy: jest.SpyInstance
  let getRecentMemoriesSpy: jest.SpyInstance
  let getCustomerBehaviorSpy: jest.SpyInstance

  beforeEach(() => {
    // 设置所有需要的 spy
    getCurrentTimeSpy = jest.spyOn(DataService, 'getCurrentTime')
    filterSOPByDateSpy = jest.spyOn(Planner, 'filterSOPByDate')
    getCustomerPortraitSpy = jest.spyOn(ContextBuilder, 'getCustomerPortrait')
    getRecentMemoriesSpy = jest.spyOn(ContextBuilder, 'getRecentMemories')
    getCustomerBehaviorSpy = jest.spyOn(ContextBuilder, 'getCustomerBehavior')
  })

  afterEach(() => {
    // 清理所有 spy
    getCurrentTimeSpy.mockRestore()
    filterSOPByDateSpy.mockRestore()
    getCustomerPortraitSpy.mockRestore()
    getRecentMemoriesSpy.mockRestore()
    getCustomerBehaviorSpy.mockRestore()
  })

  /**
     * 准备每天的 SOP 数据 map
     * 用户可以根据实际需求修改这个函数
     */
  function prepareDailySOPMap(): Record<string, any[]> {
    return {
      '-2':  [
        {
          'time': '2025-08-29 20:45:00',
          'description': '周五统一拉群'
        }
      ],
      '-1': [
        {
          'time': '2025-08-30 08:30:00',
          'description': '【7】周六催先导课'
        },
        {
          'time': '2025-08-30 19:03:00',
          'description': '介绍老师的介绍'
        }
      ],
      '0': [
        {
          'time': '2025-08-31 19:00:00',
          'description': '提醒晚上的开营仪式'
        },
        {
          'time': '2025-08-31 20:30:00',
          'description': 'DAY1前拉注意力，发送合集'
        }
      ],
      '1': [
        {
          'time': '2025-09-01 08:00:00',
          'description': 'day1早上通知第一课（分为进群和未进群人群）【所有人】'
        },
        {
          'time': '2025-09-01 11:59:59',
          'description': 'DAY1 上课前催能量测评【prompt】'
        },
        {
          'time': '2025-09-01 15:30:00',
          'description': 'Day1再次挖需（针对未回复挖需人群）'
        },
        {
          'time': '2025-09-01 18:30:00',
          'description': 'day1确认所有人有上课权限'
        },
        {
          'time': '2025-09-01 19:55:00',
          'description': ' DAY1  晚上8点的通知'
        },
        {
          'time': '2025-09-01 20:00:38',
          'description': 'day1八点开课'
        },
        {
          'time': '2025-09-01 20:05:19',
          'description': 'day1开课五分钟催到课'
        },
        {
          'time': '2025-09-01 20:30:01',
          'description': 'DAY1第一节课催到课'
        },
        {
          'time': '2025-09-01 21:00:00',
          'description': 'day1催第一节课到课第二次'
        },
        {
          'time': '2025-09-01 21:43:00',
          'description': 'day1 【动态sop】上完课回放+未完课提醒+未上课提醒'
        }
      ],
      '2': [
        {
          'time': '2025-09-02 07:30:00',
          'description': 'day2 早上课程预告（完课+未完课）（催回放第一次）'
        },
        {
          'time': '2025-09-02 11:00:33',
          'description': 'DAY2没做任何动作客户沟通'
        },
        {
          'time': '2025-09-02 11:59:59',
          'description': 'day2中午提醒：完课礼发送&提醒看完回放（催回放第2次）'
        },
        {
          'time': '2025-09-02 15:40:00',
          'description': 'day2下午第一节课未完课对话过（催回放第3次）'
        },
        {
          'time': '2025-09-02 17:03:00',
          'description': 'DAY2下午催到课回放（第4次）未回复过客户'
        },
        {
          'time': '2025-09-02 19:55:00',
          'description': 'day2 8点上课通知'
        },
        {
          'time': '2025-09-02 20:10:00',
          'description': 'day2课中10分钟'
        },
        {
          'time': '2025-09-02 20:30:24',
          'description': 'DAY2上课期间八点半催课'
        },
        {
          'time': '2025-09-02 21:15:00',
          'description': 'day2 完课人群作业财富果园画面'
        },
        {
          'time': '2025-09-02 21:20:00',
          'description': 'day2 完课人群问感受+到课提醒回放+未到课问原因提兴趣'
        },
        {
          'time': '2025-09-02 21:30:00',
          'description': 'day2 未完课人群+第一节完课（有互动）人群发回放+再次提醒财富果园'
        }
      ],
      '3': [
        {
          'time': '2025-09-03 07:30:00',
          'description': 'day3早安完课招呼预告第三节课+催财富果园+催回放+针对未看人群（第一次催回放）'
        },
        {
          'time': '2025-09-03 12:30:59',
          'description': 'day3 中午消息：催财富果园+催看完回放+预告第三节课（第2次催回放）'
        },
        {
          'time': '2025-09-03 14:30:50',
          'description': 'day3催促有财富问题的人看回放'
        },
        {
          'time': '2025-09-03 15:30:00',
          'description': 'day 3 最后一次催财富果园画面+激活低意向人群'
        },
        {
          'time': '2025-09-03 17:21:00',
          'description': 'day3  五点半预告红靴子课程&提醒没有回放'
        },
        {
          'time': '2025-09-03 18:35:32',
          'description': 'DAY3六点半语音提醒看完三节课有完课礼'
        },
        {
          'time': '2025-09-03 19:02:00',
          'description': 'day3 7点上课通知'
        },
        {
          'time': '2025-09-03 19:55:00',
          'description': 'day3 8点上课通知'
        },
        {
          'time': '2025-09-03 20:00:00',
          'description': 'day3开课时的催课通知'
        },
        {
          'time': '2025-09-03 20:05:01',
          'description': 'day3开课五分钟催到课'
        },
        {
          'time': '2025-09-03 20:30:17',
          'description': 'day3课程中催课'
        },
        {
          'time': '2025-09-03 21:10:46',
          'description': 'day3课中第三次催课'
        },
        {
          'time': '2025-09-03 22:00:00',
          'description': 'day3课中最后一次催课'
        },
        {
          'time': '2025-09-03 22:25:00',
          'description': 'Day3针对未到课人群的加播预告'
        },
        {
          'time': '2025-09-03 22:30:00',
          'description': 'Day3：下单人群预告明天课程'
        },
        {
          'time': '2025-09-03 22:30:00',
          'description': 'day3 未下单人群问感受&催回放'
        },
        {
          'time': '2025-09-03 22:40:00',
          'description': 'day3 最后一条消息：第三节课到课未下单（冥想作用+预告）'
        }
      ],
      '4': [
        {
          'time': '2025-09-04 07:32:00',
          'description': 'Day4早上好，预告+催回放'
        },
        {
          'time': '2025-09-04 09:43:00',
          'description': 'Day4调研加播课(已下单&高意向人群）'
        },
        {
          'time': '2025-09-04 12:27:00',
          'description': 'day4完课礼（第三节课到课）&高意向客户回顾'
        },
        {
          'time': '2025-09-04 14:00:00',
          'description': 'Day4通知晚上加播课'
        },
        {
          'time': '2025-09-04 15:00:00',
          'description': 'Day4：15:00提醒回放'
        },
        {
          'time': '2025-09-04 18:01:08',
          'description': 'day4下午六点发第四节课预告'
        },
        {
          'time': '2025-09-04 19:20:10',
          'description': 'day4七点分享往期案例（有视频）'
        },
        {
          'time': '2025-09-04 19:55:00',
          'description': 'day4课前十分钟提醒'
        },
        {
          'time': '2025-09-04 20:01:17',
          'description': 'day4八点上课提醒'
        },
        {
          'time': '2025-09-04 20:10:50',
          'description': 'day4课中催到课第一次'
        },
        {
          'time': '2025-09-04 20:40:07',
          'description': 'day4课中催到课第二次'
        },
        {
          'time': '2025-09-04 21:15:16',
          'description': 'day4课中催到课（第三次最后一次催）'
        },
        {
          'time': '2025-09-04 22:26:00',
          'description': 'Day4课后回访【prompt】'
        },
        {
          'time': '2025-09-04 22:30:00',
          'description': 'day4课结束后发下单链接（三四节课都到课）'
        },
        {
          'time': '2025-09-04 22:35:51',
          'description': 'day4第四节课到课给未看完人群'
        }
      ],
      '5': [
        {
          'time': '2025-09-05 08:08:00',
          'description': 'DAY5未下单打招呼整理回放链接合集'
        },
        {
          'time': '2025-09-05 11:20:48',
          'description': 'day5上午提醒看回放'
        },
        {
          'time': '2025-09-05 12:30:00',
          'description': 'DAY5中午恭喜结营'
        },
        {
          'time': '2025-09-05 14:20:26',
          'description': 'day5文字总结系统课精华'
        },
        {
          'time': '2025-09-05 16:30:55',
          'description': 'day5下午提醒第三&第四节课补课人群'
        },
        {
          'time': '2025-09-05 17:45:32',
          'description': 'day5下午问三四节课看完人没下单原因'
        },
        {
          'time': '2025-09-05 18:30:48',
          'description': 'day5晚上结营话术'
        },
        {
          'time': '2025-09-05 19:30:56',
          'description': 'day5最后一次问下单（用邮寄垫子逼单）'
        },
        {
          'time': '2025-09-05 20:20:40',
          'description': 'day5晚上分手信【prompt】-子豪版本'
        }
      ],
      '6': [
        {
          'time': '2025-09-06 09:33:47',
          'description': 'DAY6上午打招呼发总结'
        },
        {
          'time': '2025-09-06 12:19:44',
          'description': 'day6提醒看3&4节课回放'
        },
        {
          'time': '2025-09-06 15:41:07',
          'description': 'day6下午询问完课礼是否都发送'
        },
        {
          'time': '2025-09-06 17:30:41',
          'description': 'day6问询时间问题延期'
        }
      ],
      '7': [
        {
          'time': '2025-09-07 12:30:31',
          'description': 'day7学员案例分享'
        },
        {
          'time': '2025-09-07 16:00:54',
          'description': 'day7效果不满意给空间'
        },
        {
          'time': '2025-09-07 18:08:23',
          'description': 'day7周日最后一条消息用优惠名额逼单'
        }
      ]
    }
  }

  /**
     * 准备固定的客户画像数据
     * 用户可以根据实际需求修改这个函数
     *
     * 新增功能：支持根据日期获取用户画像
     * @param chat_id 聊天ID
     * @param targetDate 可选的目标日期，格式：YYYY-MM-DD。如果提供，将获取该日期的第一条消息的用户画像
     */
  async function prepareCustomerPortrait(chat_id: string, targetDate?: string): Promise<string> {
    if (targetDate) {
      // 使用新的按日期获取用户画像的功能
      return await getCustomerPortraitByDate(chat_id, targetDate)
    }

    // 默认行为：获取当前的客户画像
    return await ContextBuilder.getCustomerPortrait(chat_id)
  }

  async function getCustomerPortraitByDate(chatId: string, targetDate: string): Promise<string> {
    const userSlots =  await ChatHistoryService.getCustomerPortraitByDate(chatId, targetDate)
    const tmp = UserSlots.fromRecord(userSlots)

    return tmp.toString()
  }

  /**
     * 准备固定的记忆数据
     * 用户可以根据实际需求修改这个函数
     */
  async function prepareMemories(chat_id: string, currentDate: dayjs.Dayjs): Promise<string[]> {
    const elasticSearchResult = await ElasticSearchService.search(
      MemoryRecall.indexName,
      {
        bool: {
          must: [
            {
              term: { 'metadata.chat_id': chat_id }
            },
            {
              range: {
                'metadata.timestamp': {
                  lt: currentDate.toISOString()  // 只取 currentDate 之前的数据
                }
              }
            }
          ]
        }
      },
      5,
      {
        sort: {
          'metadata.timestamp': {
            order: 'desc'
          }
        }
      }
    )

    return elasticSearchResult.map((item) => item._source.text as string)
  }

  /**
     * 准备用户行为数据，根据当前测试日期动态生成
     * 用户可以根据实际需求修改这个函数
     */
  function prepareCustomerBehavior(day: number): string {
    if (day <= 1) {
      return '- 小讲堂海浪冥想（完成）'
    } else if (day === 2) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）`
    } else if (day === 3) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）`
    } else if (day === 4) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）
- 第三课红鞋子飞跃（已完成）`
    } else {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）
- 第三课红鞋子飞跃（已完成）
- 第四课蓝鹰预演（已完成）`
    }
  }

  it('获取 memory 测试', async () => {
    console.log(await prepareMemories('7881303394909101_1688857003605938', dayjs('2025-08-19')))
  }, 30000)

  it('should generate plans for each day from pre-course to course end', async () => {
    const chat_id = '7881303189944040_1688857949631398' // 测试用的 chat_id

    try {
      await ChatStatStoreManager.initState(chat_id)

      // 1. 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat_id)

      // 2. 计算时间范围：上课前两天到上课周结束（周日）
      const startDate = dayjs(courseStartTime).subtract(2, 'day')
      const endDate = dayjs(courseStartTime).add(6, 'day') // 周日

      console.log('测试时间范围:', startDate.format('YYYY-MM-DD'), '到', endDate.format('YYYY-MM-DD'))

      // 3. 准备每天的 SOP 数据 map
      const dailySOPMap = prepareDailySOPMap()

      // 5. 循环每一天进行测试
      let currentDate = startDate
      const results: Array<{ date: string, result: any }> = []

      while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
        MockDate.set(currentDate.toDate())

        const dayKey = currentDate.format('YYYY-MM-DD')
        const dayName = currentDate.format('dddd')

        console.log(`\n=== 测试日期: ${dayKey} (${dayName}) ===`)

        const memories = await prepareMemories(chat_id, currentDate)
        const customerPortrait = await prepareCustomerPortrait(chat_id, currentDate.format())

        getCustomerPortraitSpy.mockResolvedValue(customerPortrait)
        getRecentMemoriesSpy.mockResolvedValue(memories)

        try {
          // Mock getCurrentTime 返回当天 0点0分对应的课程时间
          const mockTime = await taskTimeToCourseTime(
            currentDate.startOf('day').toDate(),
            chat_id
          )
          getCurrentTimeSpy.mockResolvedValue(mockTime)
          console.log('Mock 时间信息:', JSON.stringify(mockTime, null, 2))

          const day = mockTime.is_course_week ? mockTime.day : mockTime.day + -8

          // Mock 当天的用户行为数据
          const customerBehavior = prepareCustomerBehavior(day)
          getCustomerBehaviorSpy.mockResolvedValue(customerBehavior)

          // Mock 当天的 SOP 数据
          const dailySOP = dailySOPMap[day] || []
          filterSOPByDateSpy.mockResolvedValue(dailySOP)
          console.log('当天 SOP 任务:', JSON.stringify(dailySOP, null, 2))

          // 调用 generatePlan
          const result = await BigPlanner.generatePlan(chat_id)

          // 记录结果
          results.push({ date: dayKey, result })

          // 输出结果摘要
          if (result) {
            console.log('生成计划成功')
            console.log('思考过程:', `${result.planResponse.think  }`)
            console.log('计划操作:', JSON.stringify(result.planResponse.plans, null, 4))
            const res = PlanOperationsSimulator.simulateOperations(result.context.existing_task, result.planResponse.plans)

            // 输出格式化结果
            const formattedResult = PlanOperationsSimulator.formatComparisonResult(res)
            console.log(formattedResult)
          } else {
            console.log('生成计划失败')
          }

        } catch (error: any) {
          console.error(`日期 ${dayKey} 测试失败:`, error.message)
          results.push({ date: dayKey, result: { error: error.message } })
        }

        // 下一天
        currentDate = currentDate.add(1, 'day')

        break
      }

    } catch (error) {
      console.error('测试设置失败:', error)
      throw error
    }
  }, 1E8) // 5分钟超时

  it('获取下一期的每天的 SOP', async () => {
    const chat_id = '7881302790061272_1688856297674847'

    const sops = await Planner.filterSOPByDate(chat_id, new Date(), new Date(Date.now() + 99 * 24 * 60 * 60 * 1000))
    console.log(JSON.stringify(sops, null, 4))
  }, 30000)

  it('should generate plan for a specific day (helper test)', async () => {
    const chat_id = '7881301047907394_1688854546332791'

    // 用户可以修改这里来测试特定的日期
    const testDate = '2025-08-16' // 修改为你想测试的日期

    try {
      const courseStartTime = await DataService.getCourseStartTime(chat_id)
      const currentDate = dayjs(testDate)

      console.log(`\n=== 单独测试日期: ${testDate} ===`)

      // 设置 mock 数据
      const customerPortrait = await prepareCustomerPortrait(chat_id)
      const memories = await prepareMemories(chat_id, currentDate)
      const customerBehavior = prepareCustomerBehavior(1)

      getCustomerPortraitSpy.mockResolvedValue(customerPortrait)
      getRecentMemoriesSpy.mockResolvedValue(memories)
      getCustomerBehaviorSpy.mockResolvedValue(customerBehavior)

      // Mock getCurrentTime
      const mockTime = await taskTimeToCourseTime(
        currentDate.startOf('day').toDate(),
        chat_id
      )
      getCurrentTimeSpy.mockResolvedValue(mockTime)

      // Mock SOP 数据
      const dailySOPMap = prepareDailySOPMap()
      const dailySOP = dailySOPMap[testDate] || []
      filterSOPByDateSpy.mockResolvedValue(dailySOP)

      // 调用 generatePlan
      const result = await BigPlanner.generatePlan(chat_id)

      // 详细输出结果
      console.log('完整结果:', JSON.stringify(result, null, 2))

      expect(result).toBeDefined()
      if (result) {
        expect(result.planResponse).toBeDefined()
        expect(result.context).toBeDefined()
      }

    } catch (error) {
      console.error('单独测试失败:', error)
      throw error
    }
  }, 60000)

})

describe('BigPlanner Tests', function () {
  beforeAll(() => {

  })

  it('Big Planner 测试', async () => {


  }, 30000)

  it('test prompt', async () => {
    const prompt = await getPrompt('test')

    console.log(await LLM.predict(prompt, { model: 'gpt-5-mini', projectName: 'playground' }, { question: { fk: 1 } }))
  }, 30000)

  it('should have plan method', async () => {
    const chat_id = '7881301047907394_1688854546332791'
    // console.log(await BigPlanner.buildContext(chat_id))
    console.log(JSON.stringify(await BigPlanner.generatePlan(chat_id), null, 4))
  }, 1E8)

  it('1231', async () => {
    const prompt = await getPrompt('free-big-plan')
    console.log(prompt)
  }, 30000)

  it('should pass', async () => {
    const promptTemplate = await PromptTemplate.fromTemplate(`# [核心角色定位]
你是一名顶级的训练营销售策略师，专注于通过 1v1 对话引导客户完成认知和信任的跃迁，最终达成“21天系统班”的转化，你需要对当天的任务进行规划

# [核心心智模型：顶级销售的思考流程]
你的所有规划都必须遵循以下三步思考闭环。这是你的第一性原则，绝不偏离。

1.  **客户状态诊断:**
    -   **成交阶段:** 客户位于哪一段？（认知→体验→承诺→成交）
    -   **关系，信任梯:** 客户现在在哪一级？（陌生→熟悉→信任→拥护）
    -   **价值感知:** 他是否从已有的课程/互动中获得了“aha moment”？他感知到的价值是情绪释放、知识获取，还是看到了解决问题的希望？
    -   **核心痛点匹配:** 我们展示的课程价值，是否精准地戳中了他暴露出来的核心痛点（例如：失眠、财富焦虑、行动力不足）？
2.  **今日策略目标:**
    -   基于诊断，明确今天最重要的“状态迁移”目标。这不是“发消息”，而是一个客户认知的转变。
3.  **对话主线设计**

# [基础信息输入]

## 课程节奏安排（必须遵守）
- 上课周前
  - 小讲堂（上课周前）：介绍 3 天课程、普及冥想作用、带练海浪减压、发送《冥想练习指南》
  - 能量测评（小讲堂中讲解）：帮助学员了解能量状态，会1对1文字解读
  - 开营仪式（上课周前，周日 20:00 社群内）：导师介绍、课程内容、上课地点
- 上课周
  - 第一课（周一 20:00~21:45）情绪减压：解析情绪与睡眠问题根源，带练【沉浸式秒睡】完课会赠送《身心对照表》
  - 第二课（周二 20:00~21:15）财富唤醒：解析财富心理障碍，带练【财富果园】课后可针对画面做一对一文字解读；完课会赠送《阿尔法音频》
  - 第三课（周三 20:00~22:20）红鞋子飞跃（核心课）：提升专注力与能量，带练【红鞋子飞跃】；完课会赠送《红鞋子音频》
  - 第四课（周四 20:00，高阶加播课）蓝鹰预演：提高行动力，预演未来（周三22:20前不公开）；完课会赠送《7天冥想会员卡》

## 销售规则与成交规律
- 商品：21天系统班课程，第三天课（周三）21:00后开始销售。
- 关键成交驱动：课程中的“获得感” + 对未来的“憧憬” + 与个人痛点的“强关联”。
- 高潜客户特征：四课全勤、持续反馈感受、主动提问。优先确保其直播到课。
- 禁止行为：第三天课前禁止任何直接销售。上课周前如果已经看完小讲堂，无需过度打扰客户，或者鼓励参与开营仪式。
- 唯一沟通方式：纯文字消息（无法发送语音、图片、链接、表情包，以及接入外部系统）

## 客户情报
- **客户画像:** {{user_slots}}
- **关键记忆/过往互动:**  {{memory}}
- **行为信号:** {{user_behavior}}
- **沉默信号:** {{silent_analyze}}

## 当前时间与阶段
- **当前时间:** {{current_time}}
- **所处阶段:** {{stage}}
- **今日核心事件:** {{today_event}}
- **明日核心事件:** {{tomorrow_event}}

# [今日已有的任务列表]
{{existing_task}}

# [输出指令]
严格按照以下逻辑和格式进行思考和输出。

1.  **[第一步：诊断与策略]** 在 "think" 字段中，先进行深入的诊断分析：
    -   **客户状态诊断:** 运用【核心心智模型】诊断客户当前的成交阶段、信任梯、价值感知、痛点匹配。**必须引用【客户情报】中的具体信息作为论据。**
    -   **核心成交阻力:** 基于诊断，用一句话总结当前最关键的成交阻力。
    -   **今日策略目标:** 定义今天要实现的，客户的“状态迁移”目标。
    -   **对话主线设计:** 规划今天对话的“微型故事”剧情。

2.  **[第二步：任务清单优化]** 接着，在 "think" 字段中，分析【今日已有的任务列表】：
    -   **评估:** 对比你设计的【对话主线】，判断现有任务哪些是匹配的、哪些是冲突的、哪些是冗余的、哪些是缺失的。

3.  **[第三步：生成JSON输出]* 
输出格式为
{
  "think": "(严格按照[输出指令]中的第一步和第二步，展示你的思考过程)",
  "plans": {
    "toAdd": [{
      "content": "(新增的任务内容，必须服务于对话主线)",
      "send_time": "(当天的发送时间，格式如：16:30)"
     }
    ],
    "toUpdate": [
      {
        "id": "（需要更新的任务ID）",
        "content": "（根据你的策略优化后的任务内容）"
      }
    ],
    "toRemove": [
      "（需要移除的任务ID）"
    ]
  }
}`, { templateFormat: 'mustache' })

    const users = [
      // Case 1｜预热期：理性型失眠人群（小叶）
      {
        user_slots: `
- 昵称：小叶（F/28，北京）
- 职业：互联网产品经理（并行项目多）
- 家庭：单身，独居
- 收入：年薪约35万
- 作息/设备：常23:30后入睡；夜醒2–3次；iPhone；微信活跃21:30–00:30
- 目标：7天把入睡时长压到≤15分钟；21天形成稳定晚间仪式
- 核心痛点：入睡困难、脑压高、晨起乏力
- 购买偏好：理性决策，要证据与复利逻辑；预算2–3k，可分期
- 抗拒点：担心"玄学"、怕浪费时间；不爱群内曝光
- 关键词：科学、可操作、效率、数据可见
`,
        memory: `
- 参与小讲堂并做海浪减压8分钟，反馈"头部压力从3降到1，有哈欠"
- 能量测评：情绪电量48/100；睡眠质量2/5；专注度3/5；焦虑7/10
- 过往：用睡眠App打卡14天后放弃；喜欢番茄钟
- 已领取并收藏《冥想练习指南》
- 私聊提问：长期失眠是否影响记忆力？
`,
        user_behavior: `
- 小讲堂准时到课（停留87分钟），提交测评表单
- 群内发言2次；私信1次；22:48完成3分钟呼吸练习打卡
- 互动时段集中在22:30–23:50
`,
        silent_analyze: `
- 18:00–21:00常沉默（通勤/晚饭），不适合长信息
- 对"科学依据/数据对照"类内容回复率高；对"价格/报名通道"暂未回应
- 建议用要点+单图格式，便于快速消化
`,
        current_time: '2025-08-16 19:10',
        stage: '认知（小讲堂当晚）',
        today_event: '小讲堂（上课周前）+能量测评引导',
        tomorrow_event: '开营仪式（周日 20:00 社群）',
        existing_task: `[
      {"id":"A2","content":"发送《冥想练习指南》并标注入门要点","send_time":"2025-08-16 22:00"},
      {"id":"A3","content":"发送能量测评表单","send_time":"2025-08-16 22:30"},
      {"id":"A4","content":"私信1句个性化测评反馈","send_time":"2025-08-16 23:15"},
      {"id":"A7","content":"发布明日20:00开营仪式海报与入群指引","send_time":"2025-08-16 23:45"}
    ]`
      },

      // Case 2｜开营仪式后：ROI导向的创业者（阿城）
      {
        user_slots: `
- 昵称：阿城（M/35，上海）
- 职业：ToB创业者（轻资产服务，现金流压力）
- 家庭：已婚一娃
- 收入：波动大（15–60万/年）
- 目标：缓解财务焦虑，提高决策清晰度
- 核心痛点：入睡易醒、白天心悸；对"回报"高度敏感
- 购买偏好：看案例与ROI；预算3–5k，可月付
- 抗拒点：担心耗时、与业务无关的"心灵课程"
`,
        memory: `
- 错过小讲堂，回放看了17分钟（倍速1.25）
- 开营仪式期间在群里问："课程对赚钱有直接帮助吗？"
- 能量测评：压力8/10；注意力3/5；睡眠3/5
- 曾学过"效率手册"类课程，能坚持2周
`,
        user_behavior: `
- 20:07进入开营直播，停留49分钟；收藏1张"课程路径图"
- 深夜23:40–00:20回复较快；白天响应慢
- 提问集中在"商业应用/ROI/案例"
`,
        silent_analyze: `
- 14:00–19:30在客户拜访，基本不看消息
- 若信息>5行，阅读率下降；偏好时间戳式要点
- 对"成功学口吻"反感，需克制营销语
`,
        current_time: '2025-08-17 21:00',
        stage: '认知→体验（开营仪式后）',
        today_event: '开营仪式（上课周前）',
        tomorrow_event: '第一课（周一 20:00 情绪减压）',
        existing_task: `[
      {"id":"B1","content":"补发小讲堂回放+3个关键片段时间戳","send_time":"2025-08-17 23:45"},
      {"id":"B3","content":"今晚22:00第一课开播提醒","send_time":"2025-08-17 21:30"},
      {"id":"B5","content":"私聊引导完成能量测评并回传截图","send_time":"2025-08-17 23:50"}
    ]`
      },

      // Case 3｜第一课后：新手妈妈（安安）
      {
        user_slots: `
- 昵称：安安（F/31，深圳）
- 身份：休产假进入尾声，新手妈妈
- 目标：改善浅眠与肩颈紧张；不打扰宝宝作息
- 核心痛点：入睡慢、夜醒后难再睡；时间切片化
- 购买偏好：短时高效音频；预算1.5–2k，需分期
- 抗拒点：担心无法连续学习
`,
        memory: `
- 第一课全程；沉浸式秒睡练习10分钟时感到"肩颈热、放松"
- Aha：看到"身心对照表"能自查症状对应练习
- 能量测评：睡眠1/5；情绪电量55/100；专注3/5
- 提及：宝宝夜奶时间大概02:30/05:30
`,
        user_behavior: `
- 课中发送"打哈欠"的表情2次；课后在群里点了"有帮助"
- 晚上23:10以后未读消息（可能忙哄娃）
- 次日10:20补看讲义3页
`,
        silent_analyze: `
- 22:30后易中断；上午10:00–12:00较空
- 喜欢"带练语音+清单式步骤"，不爱长文
- 对"时间友好型方案"敏感
`,
        current_time: '2025-08-18 22:05',
        stage: '体验（第一课后）',
        today_event: '第一课结束；赠《身心对照表》',
        tomorrow_event: '第二课（周二 20:00 财富唤醒）',
        existing_task: `[
      {"id":"C1","content":"课后发送《身心对照表》并标注自查方法","send_time":"2025-08-18 22:15"},
      {"id":"C2","content":"私聊追问"秒睡体验"并收集1句可复用见证","send_time":"2025-08-18 22:45"},
      {"id":"C3","content":"预约明天课前10分钟复训链接","send_time":"2025-08-18 23:00"},
      {"id":"C5","content":"了解家人支持度与可学习时段","send_time":"2025-08-18 23:05"}
    ]`
      },

      // Case 4｜第二课后：学生党（七七）
      {
        user_slots: `
- 昵称：七七（M/24，杭州）
- 身份：考研二战生
- 目标：提升专注与复盘效率
- 核心痛点：拖延、刷短视频、坐不住
- 购买偏好：奖学金/学生价；预算≤1.2k
- 决策链：父母有强影响力
`,
        memory: `
- 第二课全程；"财富果园"画面：土壤湿润但果实稀疏（自评"行动少"）
- Aha：意识到"躲避难题→短期快感"的循环
- 能量测评：注意力2/5；情绪电量62/100；睡眠3/5
`,
        user_behavior: `
- 课后主动交了"果园描述"，点赞6次
- 常在下午16:00–18:00在线；深夜回复慢
`,
        silent_analyze: `
- 对"同龄人案例""学习力提升曲线"感兴趣
- 对价格非常敏感，比较沉默
`,
        current_time: '2025-08-19 22:55',
        stage: '体验（第二课后）',
        today_event: '第二课结束；赠《阿尔法音频》',
        tomorrow_event: '第三课（周三 20:00 红鞋子飞跃—核心课）',
        existing_task: `[
      {"id":"D1","content":"一对一文字解读他的"财富果园"画面","send_time":"2025-08-19 23:10"},
      {"id":"D2","content":"发送《阿尔法音频》并指导午休听10分钟","send_time":"2025-08-19 23:20"},
      {"id":"D3","content":"第三课提醒+价值预告（专注力飞跃）","send_time":"2025-08-19 23:30"},
      {"id":"D4","content":"预留系统班奖学金位","send_time":"2025-08-19 23:00"},
      {"id":"D5","content":"课前试探价格敏感度","send_time":"2025-08-19 23:05"},
      {"id":"D6","content":"群内邀请其简短分享画面感受","send_time":"2025-08-19 23:15"}
    ]`
      },

      // Case 5｜第三课后（可销售期）：高意向的外企中层（Sean）
      {
        user_slots: `
- 昵称：Sean（M/42，广州）
- 职业：外企中层，带8人团队
- 目标：突破"低能量高责任"的瓶颈
- 核心痛点：疲惫与焦虑并存，周末也难休息
- 购买偏好：重视系统性、服务承诺；预算充足
- 抗拒点：担心自己"坚持不下去"
`,
        memory: `
- 第三课后21:30私信："红鞋子飞跃对我很有用"
- Aha：找到了"能量-专注-行动"闭环
- 过往：报过教练课（3个月），中途掉线
- 想要：有人监督的行动样例
`,
        user_behavior: `
- 四课基本全勤趋势；群内发言3次，影响力较高
- 资料查看及时（5分钟内）
`,
        silent_analyze: `
- 抗拒点在"能否坚持"，需提供结构化陪跑与复盘机制
- 晚上22:00后不看消息，第二天9:30前能回复
`,
        current_time: '2025-08-20 22:30',
        stage: '承诺倾向（第三课后，可销售期）',
        today_event: '第三课结束（21:00后开启销售）',
        tomorrow_event: '第四课（周四 20:00 高阶加播：蓝鹰预演）',
        existing_task: `[
      {"id":"E1","content":"邀约1v1问答，澄清目标与卡点","send_time":"2025-08-20 23:00"},
      {"id":"E2","content":"基于第三课练习生成"7天行动样例"","send_time":"2025-08-20 23:15"},
      {"id":"E3","content":"发送系统班介绍长图+学员案例","send_time":"2025-08-20 23:30"},
      {"id":"E4","content":"提供两种支付方案与退费规则说明","send_time":"2025-08-20 23:45"},
      {"id":"E5","content":"预约周四课后跟进","send_time":"2025-08-20 23:50"}
    ]`
      },

      // Case 6｜第三课前（禁止直销）：昼夜颠倒的自由职业者（阿楠）
      {
        user_slots: `
- 昵称：阿楠（M/29，成都）
- 身份：自由职业视觉设计
- 目标：把作息拉回正常，建立交付节律
- 核心痛点：拖延+昼夜颠倒；对"仪式感"有好奇
- 购买偏好：看"时间管理脚本"；预算2k左右
`,
        memory: `
- 第一、二课回放已看70%；常凌晨互动
- 朋友转介绍来的（对导师有初始信任）
- 能量测评：睡眠2/5；注意力3/5；情绪电量50/100
`,
        user_behavior: `
- 常在00:30–02:00活跃；白天不稳定
- 会收藏"工具清单"类内容
`,
        silent_analyze: `
- 对"具体操作脚本"反应最好；对"理念讲解"耐心较低
`,
        current_time: '2025-08-20 18:20',
        stage: '体验（第三课前，禁止直销）',
        today_event: '第三课（20:00–22:20，核心课）',
        tomorrow_event: '第四课预告/第三课回放与答疑',
        existing_task: `[
      {"id":"F1","content":"私信提醒第三课重点与到课福利","send_time":"2025-08-20 18:30"},
      {"id":"F2","content":"引导准备水和本子，营造仪式感","send_time":"2025-08-20 19:40"},
      {"id":"F4","content":"设置今晚"专注模式"","send_time":"2025-08-20 19:50"},
      {"id":"F6","content":"课后收集其"红鞋子飞跃"体验","send_time":"2025-08-20 23:30"}
    ]`
      },

      // Case 7｜第四课后：家庭型决策者（Lydia）
      {
        user_slots: `
- 昵称：Lydia（F/38，南京）
- 职业：小学教师，带两娃
- 目标：修复情绪耗竭，提高下午课堂能量
- 核心痛点：午后崩溃、对孩子发火后自责
- 购买偏好：分期/家庭友好作业；预算2–3k
- 决策链：丈夫为财政把关人，偏保守
`,
        memory: `
- 四课全勤；第二课后反馈"阿尔法音频午休有效"
- 第四课"蓝鹰预演"画面感强，写下未来一周清单
- Aha：意识到"能量跌落点在16:00"
`,
        user_behavior: `
- 群内愿意分享经历；作业完成度高
- 晚上21:30后不看手机；早上6:30–7:30可沟通
`,
        silent_analyze: `
活跃度较高，经常分享家庭生活
`,
        current_time: '2025-08-21 21:50',
        stage: '承诺（第四课后）',
        today_event: '第四课结束；赠《7天冥想会员卡》',
        tomorrow_event: '1v1定制咨询/锁位沟通',
        existing_task: `[
      {"id":"G1","content":"推送《7天冥想会员卡》激活提醒","send_time":"2025-08-21 22:00"},
      {"id":"G2","content":"小窗梳理其"未来预演"脚本并确认时间点","send_time":"2025-08-21 22:15"},
      {"id":"G4","content":"系统班邀约：强调家庭支持与作业轻量化","send_time":"2025-08-21 22:30"},
      {"id":"G5","content":"提供3期/6期分期方案链接","send_time":"2025-08-21 22:45"},
      {"id":"G7","content":"复盘四课中最强的"获得感"，形成个性化承诺","send_time":"2025-08-21 23:00"}
    ]`
      },

      // Case 8｜已成交：程序员新学员的入班运营（Kris）
      {
        user_slots: `
- 昵称：Kris（M/30，武汉）
- 职业：程序员，独居，轻度社交回避
- 目标：21天把注意力块提升到90分钟
- 核心痛点：晚间刷论坛停不下来；自律时好时坏
- 购买偏好：已付费，重视结构化陪跑
`,
        memory: `
- 第三课后即报名；第四课全勤
- 已开始早晨《阿尔法音频》+晚上"秒睡"组合
- 期待：学伴匹配+可视化打卡
`,
        user_behavior: `
- 00:00后基本不在线；早上7:45–8:30在线
- 热衷工具：待办清单App、日历
`,
        silent_analyze: `
- 不喜欢频繁"问候式"私聊；更喜欢明确清单与节奏
- 全程沉默
`,
        current_time: '2025-08-22 10:00',
        stage: '成交（已报名21天系统班）',
        today_event: '入班欢迎+个性化学习计划制定',
        tomorrow_event: '系统班第2天：巩固与回访',
        existing_task: `[
      {"id":"H1","content":"发送欢迎礼包：课表/学习手册/群规","send_time":"2025-08-22 10:15"},
      {"id":"H2","content":"制定个性化21天计划","send_time":"2025-08-22 14:00"},
      {"id":"H3","content":"匹配作息相近的学伴","send_time":"2025-08-22 16:00"},
      {"id":"H4","content":"第3天回访确认练习打卡情况","send_time":"2025-08-22 20:00"}
    ]`
      }
    ]

    const cases = [
      'Case 1：理性型失眠（小讲堂）',
      'Case 2：创业者关注 ROI（开营仪式后）',
      'Case 3：新手妈妈时间碎片（第一课后）',
      'Case 4：学生党预算敏感（第二课后）',
      'Case 5：高意向中层，可销售期（第三课后≥21:00）',
      'Case 6：第三课前，包含“不合规直销”任务用于测试拦截',
      'Case 7：家庭型决策，需要分期与伴侣支持（第四课后）',
      'Case 8：已成交学员，进入入班运营与陪跑场景',
    ]

    const res: any[] = []
    let i = 0
    for (const user of users) {
      const prompt = await promptTemplate.invoke(user)

      const result = await LLM.predict(prompt.value, { model: 'gpt-5-mini', maxTokens: 3000, projectName: 'undefined', responseJSON: true })

      try {
        res.push({
          user: {
            label: cases[i],
            detail: user
          },
          plan: JSON.parse(result)
        })
      } catch (e) {
        console.log(e)
      }

      i++
      console.log(i, user, JSON.stringify(result, null, 2))
    }

    console.log(JSON.stringify(res, null, 4))
  }, 1E8)

  it('123', async () => {
    console.log(await LLM.predict('hi', { model: 'gpt-5' }))
  }, 30000)


  it('json parse', async () => {
    const json: any[] = [
      {
        'user': {
          'label': 'Case 1：理性型失眠（小讲堂）'
        },
        'plan': '{\n  "think": "一、客户状态诊断（依据客户情报逐条论证）\\n- 成交阶段：认知阶段（当前正处于“小讲堂当晚”）。证据：记录显示“参与小讲堂并停留87分钟”“已领取并收藏《冥想练习指南》”。\\n- 关系/信任梯：从陌生→熟悉。证据：小窗私信1次、群内发言2次、私信提问（长期失眠是否影响记忆力），并在课中主动提交能量测评与练习打卡，说明已有初步信任但未达到完全信任/承诺。\\n- 价值感知：已有明确“获得感”（真实的生理感受）。证据：海浪减压8分钟后“头部压力从3降到1，有哈欠”，说明她体验到即时缓解（情绪/生理层面的aha）。她偏好“科学、可操作、数据可见”，因此体验需要被量化以转化为更深信任。\\n- 核心痛点匹配：课程内容高度匹配（失眠、脑压高、晨起乏力）。证据：能量测评显示“睡眠质量2/5；焦虑7/10；专注度3/5”，她的目标是“7天把入睡时长压到≤15分钟；21天形成晚间仪式”。当前课程的海浪减压、沉浸式秒睡等直接针对睡眠入手，与她痛点高度相关。\\n\\n二、核心成交阻力（一句话）\\n- 关键阻力：她担心课程有“玄学成分”、缺乏可量化/科学证据与复利逻辑，同时不愿在群内曝光，需要可验证的、私密的、以数据驱动的快感知证明。\\n\\n三、今日策略目标（明确的状态迁移）\\n- 将“熟悉”向“信任”跃迁：通过把今天的体验（海浪减压）用可视化数据与简短科学解释连结，促成她承诺参加明晚开营仪式，并愿意在接下来的7天尝试我们的睡眠打卡（至少连续3晚），同时承诺接受1次1:1测评解读（私密、非公开形式）。\\n\\n四、对话主线设计（微型故事/剧本）\\n- 开场（认同+共鸣）：基于她的反馈肯定效果——“你刚才的感受很典型：短时间内脑压下降、产生哈欠，说明下行自主神经激活，进入放松状态”。引用她的实际数据（能量测评 & 小讲堂反馈）建立关联与可信度。\\n- 解释（2-3句科学要点）：用要点式说明为何海浪减压/呼吸能在短时间降低入睡潜伏期（降低交感、提升迷走神经张力、降低大脑觉醒阈），并说明这可以“被量化”——睡眠潜伏期/夜醒次数/次日主观精力可见变化。\\n- 可操作承诺（今晚的微任务）：给出一个“≤5分钟”的夜间仪式模板（时间点、动作、期望指标：入睡≤15分钟），并提供一个简单打卡模板/表格，邀请她私下记录并次日给我回报数据（1:1，非群内）。\\n- 未来愿景（小承诺→长期）：描绘7天内可见的量化改进（如第3天入睡时间下降30%，第7天形成习惯化触发），让她看到复利逻辑与可验证路径。\\n- 消除顾虑与行动选项：明确说明课程非“玄学”，并提供“科学说明文档/参考研究”“私密1:1解读/无需群曝光”两条通路供她选择。\\n\\n五、与今日任务清单的匹配评估\\n- 匹配且必须保留/优化：\\n  - A1（群内@提醒今晚小讲堂）：匹配，但需内容调整为“要点+单图”，并加入小叶作为案例（脱敏描述）以强化社证与隐私承诺。\\n  - A2（发送《冥想练习指南》并标注入门要点）：匹配，需改为“单图要点+7天打卡模板”，便于她快速理解并量化。\\n  - A3（收集能量测评表单（自动回执））：关键且需优化：自动回执必须包含即时的“量化解读（要点）+私聊解读邀请”，以满足她的理性需求。\\n  - A4（小窗私信1句个性化测评反馈）：高度匹配，但内容需升级为“短科学解释+今晚5分钟仪式+邀请1:1解读+强调非群公开”，在她活跃时段发送（22:30–23:50）。\\n  - A6（在CRM标注标签【失眠】【脑压高】）：保留，适配打标签细化为【失眠】【脑压高】【偏好：科学/私聊】。\\n  - A7（发布明日20:00开营仪式海报与入群指引）：匹配，需在海报与文案中加“可匿名/仅听课选项”“科研/数据维度介绍”，降低曝光抗拒。\\n- 冲突/必须删除：\\n  - A5（提前报价（不合规，应删））：必须删除，第三天课前禁止任何直接销售信息。\\n- 缺失任务（为实现对话主线必须新增）：\\n  - 在她活跃时间段（22:30–23:50）发送“单图+睡眠打卡模板”，并在23:30左右跟进私聊问“今晚入睡用时/是否需要1:1？”以获得量化反馈。\\n  - 准备一条科学简短答复（针对她的私信问题：“长期失眠是否影响记忆力？”）并在A4中一并发送，满足她的理性求证。\\n  - 团队内话术卡（供导师/助教使用）以统一对外口径（19:30发送给团队），避免群里出现“玄学”表述。\\n\\n总结：今日的重点是把“体验的即时效果”转化为“可量化的信任证据”，并通过私密化、科学化的沟通路径减少她对群曝光与玄学的抗拒，从而促成她参加明日开营仪式并承诺短期（7天）睡眠打卡测试。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "（私聊｜22:35发送）一条个性化短消息：肯定今天效果+科学答复“长期失眠与记忆力”要点（2句）+今晚5分钟入睡仪式模板（步骤要点）+邀请1:1解读你的能量测评（强调非群公开、仅文字解读）",\n        "send_time": "22:35"\n      },\n      {\n        "content": "（群/私聊单图｜22:30发送）一张单图速读：‘5分钟夜间入睡仪式 + 简易睡眠打卡模板（可复制）’——要点式、数据导向，便于她保存并开始记录",\n        "send_time": "22:30"\n      },\n      {\n        "content": "（私聊跟进｜23:35发送）跟进消息：询问今晚入睡用时（填写模板1句回复即可），并提示“我会基于你的数据给出第二晚的微调建议，完全私聊）”",\n        "send_time": "23:35"\n      },\n      {\n        "content": "（内部｜19:30发送给团队）导师/助教话术卡：1) 科学解读要点 2) 如何私聊处理不愿群曝学员 3) 禁止任何销售话术提醒（第三天前）",\n        "send_time": "19:30"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "A1",\n        "content": "群内@提醒今晚20:00小讲堂，单图+要点：预告海浪减压（实际案例效果：短时间内头压下降），并提示课后可申请1:1能量测评解读（非公开）。文案采用要点式，便于夜间快速阅读。"\n      },\n      {\n        "id": "A2",\n        "content": "发送《冥想练习指南》改版为单图要点版（含‘今晚5分钟仪式’与7天睡眠打卡模板），并在文末写明：有疑问可私聊获取科学参考文献。"\n      },\n      {\n        "id": "A3",\n        "content": "收集能量测评表单（保留自动回执），自动回执内容需包含：1）即时量化解读要点（睡眠2/5意味着…三点可改进）；2）邀请私聊一对一解读（强调非公开）；3）链接/按钮：下载睡眠打卡模板。"\n      },\n      {\n        "id": "A4",\n        "content": "小窗私信升级为个性化反馈（在她活跃时段22:30–23:50发送）：包含对她测评的具体解读（引用‘睡眠质量2/5；焦虑7/10；头部压力从3降到1’），给出今晚的≤5分钟操作步骤，回答她关于记忆力的科学要点，并邀请私下继续跟踪（非群内）。"\n      },\n      {\n        "id": "A7",\n        "content": "发布明日20:00开营仪式海报与入群指引，文案加两点：可匿名参与/仅听不发言选项 & 本次侧重‘科学证据与量化跟踪’。附上如何参加的简短步骤（要点式）。"\n      },\n      {\n        "id": "A6",\n        "content": "在CRM标注标签扩展为：【失眠】【脑压高】【偏好：科学/私聊】【活跃时段：22:30–23:50】"\n      }\n    ],\n    "toRemove": [\n      "A5"\n    ]\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 2：创业者关注 ROI（开营仪式后）'
        },
        'plan': '{\n  "think": "客户状态诊断：\\n- 成交阶段：认知→体验阶段。证据：阿城错过小讲堂回放仅看17分钟（倍速1.25），参加开营仪式并在20:07进入停留49分钟，说明他对内容有兴趣并开始体验，但还未形成深度承诺。\\n- 关系/信任梯：从“陌生→熟悉”，尚未完全信任。证据：他在群里直接问“课程对赚钱有直接帮助吗？”，并收藏了“课程路径图”，显示出理性探索与怀疑并存——熟悉但信任仍需建立。 \\n- 价值感知：薄弱的“aha moment”。证据：回放仅看部分，能量测评显示压力8/10、睡眠3/5，说明痛点真实，但他还没明显从内容中获得“这能解决我问题并带来回报”的瞬间。其提问聚焦商业应用/ROI，说明他对价值的情绪（缓解焦虑）之外，更需要看到经济回报的逻辑。 \\n- 核心痛点匹配：课程内容与痛点高度相关（第一课为情绪减压/秒睡，可直接作用于入睡易醒与心悸，第二课为财富唤醒与财富果园可建立与赚钱的心理连接），但当前沟通未把“睡眠与决策清晰→业务回报”的链路说清楚，未直击其对ROI的敏感点。 \\n\\n核心成交阻力（一句话）：\\n- 他最关键的阻力是“看不到时间投入与课程成果（尤其是对生意/收益）的直接、短期可验证的回报”，同时担心课程耗时且与业务无关。证据：明确在群里问ROI、偏好看案例与ROI、抗拒成功学口吻和长信息。 \\n\\n今日策略目标（状态迁移）：\\n- 把阿城从“有兴趣但怀疑价值”的状态，迁移到“低阻力承诺参与（确认到课）并回传能量测评截图以获得个性化解读”的状态。具体表现：获得他在今晚/次日凌晨的明确回复（例如“来”或“能到”），并在私聊收到能量测评截图。意义：保证他参加周一第一课以亲体验“睡眠→决策”价值，为周三的销售打开通道。 \\n\\n对话主线设计（微型故事/节奏）：\\n- 核心剧情（3步式，信息不超5行，带时间戳式要点）：\\n  1) 共情+精简承认（30秒）——“我理解你现在更关心时间与回报，我不说大道理，只说一件事。”\\n  2) 证据短述（45秒）——一则短案例：ToB创业者通过改善睡眠/情绪，在两周内决策清晰度上升，避免了一笔亏损/促成一次合同（量化或情境化，非空泛成功学）。突出“可验证结果→保守可复制”。\\n  3) 低成本承诺（15秒）——邀请他做两件事：明晚（周一20:00）到课体验第一课并回复“来”；私聊回传能量测评截图，我做一条1句话的针对性解读，直接说明短期可观察的变化方向。 \\n- 语气要点：绝对避免“成功学”口吻，语言简短（<=5行）、有时间点、用业务导向词（决策、回报、风险、时间成本）。\\n- 最佳发送时段：23:40–00:20（阿城夜间活跃期），因此私聊核心信息安排在23:45左右发送，群内简版提醒在22:00发布（按既有任务要求）。\\n\\n今日已有任务列表评估：\\n现有任务：\\n- B1 补发小讲堂回放+3个关键片段时间戳：匹配（但需调整为ROI导向的三个时间戳，且每个片段一句“对你有何价值”）。\\n- B2 邀请在群里分享“财富卡点”1句话：部分匹配，但形式需更精简并且引导出商业价值点；否则可能产生泛感性回复。建议改为“请在群里用一句话说出今晚最有助于赚钱的点”并限定回复格式（<=15字）。\\n- B3 今晚22:00设置第一课开播提醒：匹配（社区提醒是必要的）。注意提醒语不提售卖，只强调“带练+能改善睡眠/决策”。\\n- B4 发送价格（不合规，应删）：冲突，必须移除（平台规则/销售规则明令禁止第三天课前售卖）。\\n- B5 私聊引导完成能量测评并回传截图：高度匹配，且是今天要达到的关键承诺之一。须在他活跃时间发出精简私聊并承诺1句性解读作为回报。避免超过5行。\\n- B6 准备“ROI常见疑问”简版FAQ卡片：匹配且必要，但要严格控制字数（不超5行）并用时间戳/点格式呈现。优先做一版超简短的“3问答”。\\n\\n缺失的任务（必须新增）：\\n- 一条针对阿城的1v1私聊脚本（23:45发送），把对话主线落地：共情→ToB案例→低成本到课承诺→回传测评截图。必须控制在3–5行，并带清晰CTA（回复“来” + 发送截图）。\\n- 一条超短的ToB创业者ROI案例卡（群内可见或私发，1–2句话+数字/情景），用于直接回应他在群里关于“课程对赚钱有没有帮助”的疑问。 \\n\\n总体结论：保留并优化B1、B3、B5、B6；升级B2为更指向性的群内互动；移除B4；新增一条针对性私聊与一条ToB案例卡。所有对外/群发内容必须严格<=5行、带时间戳式要点、避免成功学表述，并安排在阿城高响应时间段（23:40–00:20）发送。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "23:45 私聊给阿城（<=5行）：1) 我知道你最在意的是时间和回报；2) 简短案例：某ToB创始人在两周内通过改善睡眠把一个决策拖延问题解决，避免一笔潜在亏损（情景化说明）；3) 低成本邀请：明晚第一课（周一20:00）来体验，回传能量测评截图我给你一句性解读。请回复“来”并发截图。",\n        "send_time": "23:45"\n      },\n      {\n        "content": "23:50 ToB创业者ROI微案例卡（群发或私发回应阿城提问，<=2行）：睡眠/情绪直接影响决策速度与风险判断——短期可见变化：更少冲动决策、提高谈判时的清晰度（示例：避免一次失误订单/促成一次续约）。",\n        "send_time": "23:50"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "B1",\n        "content": "补发小讲堂回放+3个关键片段时间戳（每个片段后加一句‘对决策/睡眠/赚钱的直接意义’，总信息<=5行，强调第一个片段为‘如何快速改善睡眠以提升白天决策’）。"\n      },\n      {\n        "id": "B2",\n        "content": "改为群内精简互动：邀请大家用一句话（<=15字）写出今晚最有助于‘赚钱/决策’的点，针对性引出商业价值点（发送时间21:30/或根据群活跃调整）。"\n      },\n      {\n        "id": "B3",\n        "content": "今晚22:00 在群内设置并发布第一课（周一20:00）开课提醒，说明重点：‘情绪减压→沉浸式秒睡→提升决策清晰度（对创业者的直接好处）’（<=3行）。"\n      },\n      {\n        "id": "B5",\n        "content": "私聊引导完成能量测评并回传截图：在23:45的私聊中一并请求截图，承诺1句性解读（不要超过5行），并用‘发送截图即可’作为低摩擦CTA。"\n      },\n      {\n        "id": "B6",\n        "content": "准备并发送‘ROI常见疑问’超精简FAQ卡（不超5行），重点回答：时间成本如何最小化、如何看到短期业务相关效果、典型ToB案例。发送时间：23:50群内或私聊备用。"\n      }\n    ],\n    "toRemove": [\n      "B4"\n    ]\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 3：新手妈妈时间碎片（第一课后）'
        },
        'plan': '{\n  "think": "第一步·客户状态诊断（引用客户情报）\\n- 成交阶段：体验阶段（已完成第一课，全程到课并参与沉浸式秒睡练习10分钟）。证据：\\"第一课全程；沉浸式秒睡练习10分钟时感到‘肩颈热、放松’\\"。\\n- 关系/信任梯：从陌生→熟悉→初步信任。证据：课中主动发“打哈欠”表情、课后在群里点了“有帮助”、次日10:20补看讲义3页，显示持续关注与正面反馈。\\n- 价值感知：已有“aha moment”——《身心对照表》让她能自查（已明确），同时练习带来即时身体感受（肩颈热、放松），属于“情绪/身体释放＋看到可行练习”的价值体验。\\n- 核心痛点匹配：课程价值已部分精准命中她的痛点（入睡慢、夜醒难再睡、时间切片化）。证据：能量测评睡眠1/5，专注3/5；并提供了可操作的工具（身心对照表、秒睡练习）。但她关心“不会连续学习/时间不够”，且宝宝夜奶在02:30与05:30，说明必须把干预设计为“极短、可在夜醒时使用”的工具。\\n\\n核心成交阻力（1句）：她最关键的抗拒是“担心无法连续学习/无法把练习嵌入碎片化的育儿作息”（已明确为抗拒点）。\\n\\n第二步·今日策略目标（状态迁移）\\n- 目标：把“体验到的片刻放松”转化为“可随时复用的短时工具并愿意在次日继续参与”的认知迁移。具体端点：让安安接受并收藏1个90–120秒的夜间微练习（并在次日告诉我们感受），同时确认她会直播/补看明晚第二课（或至少19:50前进入复训链接）。替代性小目标：取得一条可复用的1句见证（用于后续社群信任背书）。\\n\\n第三步·对话主线设计（微型故事）\\n- 场景起：课后致谢+立即交付《身心对照表》并标注“夜间3步自查”——强化她已有的aha。\\n- 短时工具：送出一段90–120秒的带练语音（“宝宝夜醒友好：肩颈放松+呼吸”），明确说明这是可以在02:30/05:30夜醒时使用的不打扰宝宝的练习。\\n- 互动要求：请求她在明早（10:20）给一句简单感受（1句话），并征求是否可以用作匿名见证。（语句简短、易答）\\n- 时间承诺承接：19:50发送复训链接及一句“来就给你课后财富画面一对一文字解读”的温和邀请（这是课程固有福利，非销售），以提高到课概率。\\n- 关闭与后续：记录家庭支持与可学习时段（通过两项封闭式问题），并在内部准备“时间友好学习方案”供随时发给她（但关于付费/报名的任何直接沟通，须等到周三21:00后）。\\n\\n今日已有任务清单评估（与对话主线比对）\\n- C1（课后发送《身心对照表》并标注自查方法）：匹配。需要补充“夜间3步自查”与一条短语音配套，发送时短、清晰，优先。→ 保留并优化。\\n- C2（私聊追问“秒睡体验”并收集1句可复用见证）：匹配，但发送时机需改为次日10:20（她偏好上午10:00–12:00，且当晚22:30后易中断），并用极简模板（一句问题+选项）。→ 更新发送时机与话术。\\n- C3（预约明天课前10分钟复训链接）：匹配，但要具体到19:50发送，并附1句激励（免费课后文字解读画面）。→ 更新细化。\\n- C4（白天群内硬广21天课程）：冲突且不合规（已标注应删）。→ 移除。\\n- C5（了解家人支持度与可学习时段）：匹配，但需用两道封闭问题，且与C2合并在次日10:20的简短私聊里完成，避免多次打扰。→ 更新合并。\\n- C6（在CRM记录标签【时间碎片】【年轻妈妈】）：匹配，建议补充更细标签（夜奶时间、音频偏好、预算区间）以便后续个性化跟进。→ 更新扩展标签。\\n- 缺失任务：\\n  1) 立即发送并固定一段“90–120秒夜间微练习语音”（服务于对话主线，满足她对短时高效音频偏好）。\\n  2) 次日10:20的简短跟进私聊（合并C2+C5），目的：收集1句见证、确认可学习时段与家人支持度。\\n  3) 18:30的温和课堂邀请提醒（提前19:50复训链接前的预热），强化到课率。\\n  4) 内部准备任务：制定一份“时间友好学习方案（2套）”供周三销售开启后个性化匹配（不对学员直接推售）。\\n\\n结论：主线是“交付可落地的短练习 → 在可接受的时间点索取简短社会证明 → 提前绑定次日到课意愿”，同时严格遵守第三天课前不得销售的规则。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "发送90–120秒的夜间微练习语音（宝宝夜醒友好：肩颈放松+呼吸），附一句使用说明：“在02:30/05:30夜醒时，静坐/半躺，听90秒 → 轻缓呼吸3轮即可，不用起身哄娃”。",\n        "send_time": "22:15"\n      },\n      {\n        "content": "次日10:20——私聊合并跟进（合并C2+C5）：1）你昨晚试了短练习吗？用一句话形容当下感受（例如：‘肩颈放松，入睡快’）——可用于匿名见证；2）家里是否有人能帮忙15分钟/晚？你最可行的练习时段是（A 早上10:00–12:00；B 午睡前；C 夜醒后短练习）——选项式回答。——语气极简，便于快速回复。",\n        "send_time": "10:20"\n      },\n      {\n        "content": "18:30——温和邀请消息（群/私聊视情境）：提醒明晚20:00财富唤醒课程，说明“到课后我们可以为你的财富画面做一对一文字解读（课程福利）”，目的是提高19:50复训链接点击率和到课率（非销售）。",\n        "send_time": "18:30"\n      },\n      {\n        "content": "内部准备：制定“一页时间友好学习方案（两套）”——A：夜间/分批90秒练习路线；B：早晨/午休10–15分钟集中特训路线（含分期预算选项备选文案，注意仅内部素材，周三后外发）。",\n        "send_time": "15:00"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "C1",\n        "content": "课后发送《身心对照表》，并在开头标注『夜间3步自查』：1) 记录入睡时长；2) 夜醒后试90秒微练习并记录是否能回睡；3) 次日早上用1句描述睡眠质量；同时附上今天的90–120秒带练语音链接/文件。"\n      },\n      {\n        "id": "C2",\n        "content": "私聊追问“秒睡体验”并收集1句可复用见证（话术改为次日10:20发送，问题极简：‘昨晚试了短练习吗？一句话感受～（可匿名作见证）’）。"\n      },\n      {\n        "id": "C3",\n        "content": "预约并在次日晚19:50发送明天（周二）20:00财富唤醒课前的复训/进入链接，附一句简短激励：‘来听课后我给你做一对一文字解读你的画面（课程福利）’。"\n      },\n      {\n        "id": "C5",\n        "content": "了解家人支持度与可学习时段——与C2合并为次日10:20的闭合式私聊（两道选择题：家人是否能帮忙15分钟？你最可行的练习时段哪个？）。"\n      },\n      {\n        "id": "C6",\n        "content": "在CRM记录标签扩展为：【时间碎片】【年轻妈妈】【夜奶02:30/05:30】【短时音频偏好】【预算1.5-2k】【全勤意向-已第一课】"\n      }\n    ],\n    "toRemove": [\n      "C4"\n    ]\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 4：学生党预算敏感（第二课后）'
        },
        'plan': '{\n  "think": "一、客户状态诊断（严格依据【核心心智模型】与【客户情报】）\\n- 成交阶段：体验阶段（客户已完整参与第二课【财富果园】，处于从“认知→体验”向“承诺”过渡的窗口）。依据：客户“第二课全程”“课后主动交了果园描述、点赞6次”，说明体验度高且有参与感。\\n- 关系/信任梯：熟悉→初步信任。依据：主动互动（画面提交、点赞）、对课程内容产生“Aha（意识到躲避→短期快感循环）”，但未表现出付费意向与对导师的深度拥护，且对价格非常敏感，决策受父母影响，说明信任还未到可直接触发付费的“拥护”级别。\\n- 价值感知：存在明确的“认知型Aha”：她已经意识到自己的回避循环（见果园自评“行动少”），并从课程获得自我觉察。但目前获得感偏向“洞见/情绪释放”，尚未形成“立即可感知的能力/状态改善”的体验（比如短期内专注提升的证明）。能量测评证据：注意力2/5、情绪62/100、睡眠3/5，说明她客观上缺乏高质量注意力状态，渴望快速可复制的方法。\\n- 核心痛点匹配：课程价值与痛点高度相关。她的痛点是“拖延、刷短视频、坐不住”，而明日的第三课（红鞋子飞跃）正是提升专注与能量的核心课；她对“同龄人案例”“学习力提升曲线”感兴趣，说明对“能看见效果路线图”的内容敏感。\\n\\n二、当前最关键成交阻力（1句）\\n- 她对价格高度敏感且决策需父母参与，同时仍缺少一次短期内可感知的“专注获得感”，无法促成内心对报名付费的明确愿景。\\n\\n三、今日策略目标（明确的“状态迁移”）\\n- 把“洞见型价值”（认识到自己回避）转化为“可体验的获得感与未来憧憬”：让七七承诺参加明晚的第三课并先完成一个小型的20分钟专注验证（微承诺），通过即时体验产生对课程效果的信任，从“体验”过渡到“愿意在第三课后考虑承诺/决策”的心理位置（尚未直接销售）。同时温和探测并准备支持其决策链（父母/奖学金需求）。\\n\\n四、对话主线设计（微型故事/剧情）\\n- 开场（今晚，温和肯定）：针对她的“果园”描述做一段高共鸣解读（肯定观察→用注意力/睡眠数据连结原因），让她感觉被看见；强化她已经有“洞见”的价值。\\n- 触发小承诺（明日16:30–17:00）：邀请她参加一个“20分钟专注挑战”（可操作、风险低、立即见效），约定结束后把“完成的第一件事”回给我——通过小胜利建立获得感。\\n- 链接未来（今晚到明晚）：把小胜利与明晚“红鞋子飞跃”课堂的承诺连接起来，做“放大承诺”：说“明晚我们在课里把这个状态放大并给出可复制路线图”，激发对课程的憧憬。\\n- 决策支持（明日17:30–19:00）：以开放式探询了解决策链（会和谁商量？父母最关心什么？学生价/奖学金是否会促成决定？），并承诺如果她有需要，提供一页“给家长的关切清单”以便沟通（这是软化价格障碍的准备动作，但不提前推售）。\\n\\n五、今日已有任务列表与对话主线的匹配评估\\n- D1（一对一文字解读“财富果园”画面）：高度匹配。是建立被看见与把洞见转为可操作建议的关键环节，应保留并升级为带有“微承诺/挑战”的引导。理由：她已提交且对果园反思深度高。\\n- D2（发送《阿尔法音频》并指导午休听10分钟）：匹配，但需时间调整与听法说明。当前时间晚，直接发并说明今晚睡前与明日午休两种听法更合适；并要求简单回执以确认体验（可以作为后续跟进触发）。\\n- D3（第三课提醒+价值预告）：高度匹配，但信息语气要避开售前推销，强调“明晚课堂能带来立即可感知的专注体验”，并确认其到课承诺（小承诺）。同时安排次日傍晚的最后提醒（她活跃于16:00–18:00）。\\n- D4（预留系统班奖学金位（准备但不宣传））：匹配（内部动作），必须保留为内部准备以便第三课销售窗口开启后快速响应。不要对学员公开。\\n- D5（课前试探价格敏感度（不直接报价））：匹配，但当前时点问法需要更策略化（先探决策链、父母关注点，再探预算）。应把试探时间放在明日16:30–18:00她在线窗口内，并以“我能帮忙准备给父母看的摘要吗？”的方式软探。\\n- D6（群内邀请其简短分享画面感受）：有价值但存在轻微风险（如果她偏内向，可能产生压力）。建议改为“先私聊鼓励，再邀请自愿分享到群”，以保护关系并促成自愿曝光。\\n\\n六、结论（缺失项与调整建议）\\n- 缺失：明确的“体验联结动作”（即20分钟专注挑战）与“家长沟通支持物”（1页家长关切清单），两者都是把洞见转化为获得感与降低购买阻力的必需品。\\n- 冲突/需调整：D2需要时间与听法说明；D6需从强邀请改为“私聊优先，愿意再发群”。\\n\\n基于以上诊断和剧情设计，我将对现有任务进行优化（见 plans 部分）。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "今晚私聊（个性化解读+微承诺）：简短肯定你对“果园”观察（“土壤湿润但果实稀疏＝行动少”）→用你的注意力2/5、睡眠3/5说明可能机制→给出一个立即可做的微操作：今晚睡前写1件次日最重要的事，并邀请你明日16:30/17:00参加20分钟专注挑战（完成后把你完成的第一件事回给我）。",\n        "send_time": "23:10"\n      },\n      {\n        "content": "发送《阿尔法音频》：已发送文件并附听法建议——今晚睡前听10分钟作为安抚，明日午休12:30–13:30听10分钟作为专注热身；听后请简短回复“听过+一句感受”。",\n        "send_time": "23:15"\n      },\n      {\n        "content": "明日微挑战发起（客户参与）: 今天16:30/17:00开始20分钟番茄专注挑战，结束后把“完成的第一件事”回我（这是一个验证专注获得感的小实验，明晚的红鞋子课会把这个状态放大）。",\n        "send_time": "16:30"\n      },\n      {\n        "content": "明日17:30（在线窗口）温和试探决策链：开放式问句（例如：如果你觉得明晚有效，你会和谁商量决定？父母一般关心哪些点？学生价或奖学金会不会让你更容易决定？需要我准备一页给家长看的关切清单吗？）——此消息用于非直接报价的价格敏感度试探与准备家长支持材料。",\n        "send_time": "17:30"\n      },\n      {\n        "content": "明日18:30（到课提醒）：温柔提醒明晚20:00的红鞋子飞跃（核心课），提示提前准备安静环境和纸笔，并再次承诺课堂将带来可复制的“立即专注”练习（不涉及售卖）。",\n        "send_time": "18:30"\n      },\n      {\n        "content": "内部任务（不对学员公开）：预留系统班奖学金位与准备1页家长沟通卡片（包含：课程目标、短期可见收益、学生价/奖学金说明），以便第三天销售窗口打开时快速支持学员决策。",\n        "send_time": "23:50"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "D1",\n        "content": "一对一文字解读他的“财富果园”画面（升级）：肯定她的观察→用注意力2/5、睡眠3/5数据连结解释（为什么会‘果实稀疏’）→给1个今晚可执行微操作（睡前写次日最重要事）→邀请明日16:30/17:00参加20分钟专注挑战并约定回馈（以建立即时获得感）。"\n      },\n      {\n        "id": "D2",\n        "content": "发送《阿尔法音频》并指导听法（升级）：今晚已发送并建议今晚睡前听10分钟做安抚、明日午休12:30–13:30听10分钟做专注热身；要求简短回执（‘听过+一句感受’）以便后续跟进。"\n      },\n      {\n        "id": "D3",\n        "content": "第三课提醒+价值预告（调整语气）：今晚/次日提醒以‘体验承诺’为核心（强调明晚红鞋子如何直接协助坐不住/拖延问题并带来一次可复制的专注状态），邀请其到课并完成明日的20分钟专注小实验；严禁任何售前报价或压力性推销。"\n      },\n      {\n        "id": "D4",\n        "content": "预留系统班奖学金位（内部，不对学员宣传）：已准备好奖学金位与快速审批流程，以便第三天销售窗口开放后内部优先分配。"\n      },\n      {\n        "id": "D5",\n        "content": "课前试探价格敏感度（策略化）：将试探方式从直接问价转为探决策链（会和谁商量、父母关心点、学生价/奖学金的影响），执行时间放在学员在线窗口（明日17:30），并在得到信号后提供‘家长沟通清单’（非推销，只是支持）。"\n      },\n      {\n        "id": "D6",\n        "content": "群内邀请其简短分享画面感受（调整为二步走）：先私聊鼓励其写1句感受（降低曝光压力），若她愿意再将其简短摘录同步到群以增强社会证明；建议私聊邀请发送时间今晚23:25。"\n      }\n    ],\n    "toRemove": []\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 5：高意向中层，可销售期（第三课后≥21:00）'
        },
        'plan': '{\n  "think": "客户状态诊断:\\n- 成交阶段：承诺倾向（第三课已结束，销售窗口已开启）。证据：第三课后21:30他私信“红鞋子飞跃对我很有用”，并明确表示获得“Aha：能量-专注-行动闭环”。\\n- 关系/信任梯：从熟悉向信任跃迁，接近“信任”级别。证据：四课基本全勤趋势、群内发言3次、资料查看及时（5分钟内）。\\n- 价值感知：已有明显获得感（情绪释放+结构化认知），尤其在“能量→专注→行动”闭环上看到了希望。证据：私信反馈与课堂互动积极。\\n- 核心痛点匹配：课程价值高度契合其痛点（“低能量高责任”、疲惫与焦虑、周末难休息），第三课的“红鞋子飞跃”直接触及了专注与能量管理。\\n\\n核心成交阻力:\\n- 一句话：Sean最关键的抵触是“能否坚持/会不会中途掉队”（历史上曾报3个月教练课但掉线），他需要结构化、可被监督且可交付的小步成就感与保障。\\n\\n今日策略目标:\\n- 将“承诺倾向”转化为对“陪跑+可执行方案”的明确认同（他说出“我要有人监督的行动样例/我愿意试一个有陪跑机制的版本”或直接预约付款意向），即把模糊的好感变为具体的执行承诺（先从7天试验承诺到完整21天为路径）。\\n\\n对话主线设计（微型故事）:\\n1) 开场：感谢+认知复盘——先肯定他在第三课的收获（引用他21:30私信的话）。\\n2) 放大痛点并建立共情——强调“责任越大越容易被能量拖累”并指出历史掉线风险（他曾中途掉线），说明这是普遍陷阱。\\n3) 解决方案呈现——提出“为你定制的‘可监督的7天起跑包’”（来自课程练习的具体日程，每日任务、监督点、短回顾），并说明这和21天系统班的衔接与陪跑承诺。列出具体保障（导师1v1三次、日打卡+周回顾、退费/保障条款）。\\n4) 低阻力行动邀请——给出两种清晰选择：A. 先试7天陪跑包（小额/免费试学+承诺回顾）；B. 直接报名21天系统班（分期/退费保障）。并提供预约10分钟确认的时间段与付款入口。\\n5) 收束+社会证明——引用群内他与其他类似学员的互动（用作社证），并提示名额/服务承诺以促成及时决策。\\n\\n现有任务列表评估（与对话主线对比）:\\n- E1（21:40小窗邀约1v1问答）：匹配但时间已错过（他22:00后不看消息）。需要重排并把重点转为“早安9:35的快速确认+预约通话链接”。\\n- E2（基于第三课练习生成“7天行动样例”）：高度匹配，必须强化为“为Sean定制”的版本，突出每日监督点与低门槛小胜利，作为切入点转换承诺。\\n- E3（发送系统班介绍长图+学员案例）：匹配，但发送语需高度个性化（引用他的Aha和担忧），并强调陪跑与退款保障；发送时机应在他可见时间窗（早上9:30后）。\\n- E4（提供两种支付方案与退费规则说明）：匹配，必须加入“试学/陪跑”选项与小额预付或分期，明确退费条件以降低其“坚持不下去”的恐惧。\\n- E5（预约周四课后跟进）：匹配且必要，保留并在对话中作为后续承诺节点（若他未立即决定，确保周四课后再约）。\\n- E6（统计其群内发言作为社证素材）：匹配且重要，应把他自己的发言整理成可复用的社证短句（引用他对红鞋子的评价）用于消息里增强共鸣。\\n\\n缺失的任务（必须补充）:\\n- 制作并发送“个性化陪跑承诺书/计划表”（可签收的小任务清单，消除他“坚持不下去”的不确定性）。\\n- 设计一条高情感命中且低侵入的私信话术模板，聚焦“监督+小步兑现+保障”，并在早上9:35窗口发送。\\n- 设定跟进规则：若9:35消息无反应，12:00发第一轮温和提醒，次日9:35再发第二轮（但当前只规划当日策略）。\\n\\n结论：保留并强化E2/E3/E4/E5/E6，更新E1为次日早晨的1v1邀约。新增个性化陪跑承诺和发送时点以及两个上午内的跟进节点，确保消息在Sean可见时送出并以可执行的“陪跑承诺”打消其坚持焦虑。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "早安1v1私信（个性化邀约）：引用他21:30反馈+一句话复盘（能量-专注-行动），提出“为你定制的7天陪跑起跑包（含日打卡、导师1v1三次、周回顾）”，并附上10分钟预约链接；低门槛选项：7天试学或直接21天分期报名。目的是让他选择“我愿意试/我要报名”。",\n        "send_time": "09:35"\n      },\n      {\n        "content": "发送为Sean定制的“7天行动样例”（基于第三课练习）：每天具体任务（时长15-30分钟）、监督点（打卡格式）、每日小目标与完成样例，突出第3天与第5天的可量化回报点，强调导师会在每天第2条反馈中打分/建议。",\n        "send_time": "09:40"\n      },\n      {\n        "content": "发送系统班介绍长图+学员案例（个性化文案）：把长图和一位与Sean职业/责任相似学员的成功案例合并，文案第一句就回应他的担忧（‘担心坚持不下去？我们先陪你7天起跑’），并在末尾放两套支付方案与退费/保障要点的简洁说明与报名入口。",\n        "send_time": "09:50"\n      },\n      {\n        "content": "当天首轮跟进（若无回复于12:00发送）：简短温和提醒，重复价值点（陪跑+小步兑现）并提醒“名额/导师陪跑配额有限，优先保留给已预约者”，并附上快速确认按钮文案（我愿试/我报名）。",\n        "send_time": "12:00"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "E1",\n        "content": "将21:40的小窗邀约重设为次日09:35的早安1v1私信，内容改为：感谢+引用其私信反馈+提出‘7天陪跑起跑包’与10分钟预约链接，重点解决‘能否坚持’的担忧。"\n      },\n      {\n        "id": "E2",\n        "content": "基于第三课练习生成并个性化的‘7天行动样例’（针对Sean）：每日任务、时长、打卡格式、导师反馈点、预期小胜利节点（第3天/第7天），准备为09:40发送。"\n      },\n      {\n        "id": "E3",\n        "content": "发送系统班介绍长图+学员案例（可销售期），改为09:50发送，文案首段直接回应他‘坚持’的担忧并突出陪跑承诺与退费保障；附社证（摘自其群内发言）以增强共鸣。"\n      },\n      {\n        "id": "E4",\n        "content": "提供两种支付方案与退费规则说明（加入试学/陪跑选项与小额预付/分期、明确退费条件与时间节点），并在09:50的介绍消息末尾一并发送支付入口。"\n      },\n      {\n        "id": "E5",\n        "content": "预约周四课后跟进：若Sean未在当天确认，自动在周四课后（周四22:10）发起一条1v1跟进，内容为‘回顾你的7天试跑情况并决定是否过渡到21天系统班’，并准备当场转化话术。"\n      },\n      {\n        "id": "E6",\n        "content": "统计并摘录其群内发言作为社证素材：挑选3条高共鸣语句（含他对红鞋子的评价），整理为可复制引用句，用在私信第1段和系统班介绍文案中。"\n      }\n    ],\n    "toRemove": []\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 6：第三课前，包含“不合规直销”任务用于测试拦截'
        },
        'plan': '{\n  "think": "第一步：诊断与策略\\n\\n客户状态诊断（依据客户情报）\\n- 成交阶段：体验阶段（当前阶段明确给出：第三课前，禁止直销），客户已收看“第一、二课回放已看70%”。\\n- 关系/信任梯：熟悉→初步信任。论据：朋友转介绍（对导师有初始信任），且已持续看课并在凌晨时段互动，表示对内容有粘性，但尚未表现出明确的承诺或付费意向。\\n- 价值感知：局部获得感偏“工具/操作”方向。论据：会收藏“工具清单”类内容、对“具体操作脚本”反应最好；能量测评显示“睡眠2/5、注意力3/5、情绪电量50/100”，说明他更需要可落地的改善方案而非抽象理念。\\n- 核心痛点匹配：高度吻合。目标是“把作息拉回正常、建立交付节律”；核心痛点为“拖延+昼夜颠倒；对仪式感好奇”。课程前三课布局（尤其第三课的红鞋子飞跃）与其需求（专注力、仪式、睡眠改善）强相关。\\n\\n核心成交阻力（一句话）\\n- 当前最关键的阻力是：客户需要“立刻可执行的、针对夜猫子/交付场景的时间管理+专注脚本”并在第三课中获得一次直观的获得感，否则不会自发进入承诺阶段。\\n\\n今日策略目标（状态迁移）\\n- 目标：把阿楠从“被动体验者”迁移为“在第三课获得明显的专注/睡眠体验并口头表达想把方法系统化”的状态（即体验→产生强烈想持续练习的意向，为后续承诺/成交奠定情感与逻辑基础）。\\n\\n对话主线设计（微型故事）\\n- 剧情线：夜深的设计师阿楠拿到一份“夜猫子专注脚本”，按步骤设置专注模式和仪式（水、本子、专注脚本），在第三课中通过“红鞋子飞跃”获得一次短时间内的深度专注/能量回升体验（明显感到能量与入睡节律被触及），课后立刻把体验反馈给我并询问“如何把这种状态变成常态”。这条故事强调：可执行脚本 → 课堂深度体验 → 立即反馈与好奇（为未来承诺预热）。\\n\\n第二步：对今日已有任务列表评估（对比主线）\\n- F1（18:30前私信提醒第三课重点与到课福利）\\n  评估：匹配。理由：提醒可提高到课率且强化“到课福利”的获得预期。建议保持，但要强调“具体操作脚本/仪式”而非抽象福利。\\n\\n- F2（引导准备水和本子，营造仪式感）\\n  评估：匹配。理由：客户对仪式感有好奇，且仪式能放大课堂沉浸感。\\n\\n- F3（先发系统班价格（不合规，应删））\\n  评估：冲突且必须删除。理由：第三天课前禁止任何直接销售，发价格会触犯规则并破坏体验阶段的节奏。\\n\\n- F4（帮其设置今晚“专注模式”手机脚本）\\n  评估：匹配但需升级。理由：这是关键推进点（客户喜好脚本），但当前任务表述泛泛，应给出“夜猫子版本的逐步脚本+小验证（10分钟）”并配合微奖励机制，时间节点要与上课前准备紧密衔接。\\n\\n- F5（整理前两课反馈并录入CRM）\\n  评估：匹配且必要。理由：有助于后续个性化引导，但要在今日18:45前完成，以便基于反馈发送更精准的预热消息。\\n\\n- F6（课后收集其“红鞋子飞跃”体验）\\n  评估：匹配但需明确时点与提问框架。理由：必须在课堂结束后立即触达（22:20–22:45）收集感受，问题要聚焦“具体变化（专注、呼吸、想法、睡意）”并收集是否愿意把效果延展为日常练习的意向。\\n\\n缺失项（基于对话主线应补足的任务）\\n- 缺少“时间管理脚本样例（夜猫子交付脚本）”的预先投放——客户对脚本敏感，需要在上课前看到一段可执行样例来降低心智阻力。\\n- 缺少上课前的短问（1题 sondage）以激发到课目的性（例如：“今晚你最想在课堂解决的是：A 入睡更快 B 白天专注 C 按时交付”）。\\n- 缺少“19:58—20:00 的最后一条私信开课引导话术”，用于把凌晨活跃客户拉进课堂并提供“一句式操作指引”。\\n- 缺少课中实时小刺激（例如在21:05–21:20私信提示“现在是关键带练时刻，深呼吸并专注脚步画面”）以提升课堂内体验强度（需谨慎、不打断课堂节奏）。\\n\\n结论（执行重点排序）\\n1) 立即删除F3；2) 升级F4为“夜猫子专注手机脚本+10分钟预演任务”；3) 在19:10–19:30投放1页“时间管理脚本样例（夜交付版）”；4) 19:30发1题短问，激活到课动机；5) 19:58最后一条私信引导；6) 课后22:30–22:50立即收集F6反馈并记录CRM（F5）以便翌日跟进。\\n",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "19:10 发送“夜猫子时间管理脚本（样例）”：一页可复制的交付节律脚本（含：22:30–23:00预备仪式、00:00–01:00深度工作段、短休奖励、上课前30分钟降噪步骤），突出“10分钟可验证的专注练习”。",\n        "send_time": "19:10"\n      },\n      {\n        "content": "19:30 发1题短问（单选）：“今晚你最想解决的是？A 入睡更快 B 白天专注提升 C 按时交付”。用以激发到课目的性并收集偏好。",\n        "send_time": "19:30"\n      },\n      {\n        "content": "19:50 发送“开课前准备清单+专注模式脚本（夜猫子版）”：具体步骤（静音非必要通知、打开计时器、准备水+本子、耳机音量、10分钟呼吸/下压脚本），并附带3句可直接复制的桌面/手机状态文案（便于他一键设置“勿扰”）。",\n        "send_time": "19:50"\n      },\n      {\n        "content": "19:58 私信一句开课提醒话术：简短、指令性强（如“20:00准时上线→到时我们带你做一个10分钟红鞋子预热，记得带本子”），目的是把凌晨活跃客户拉进课堂。准备好在20:00前再次确认其是否顺利上线。",\n        "send_time": "19:58"\n      },\n      {\n        "content": "21:05（课中）发送“短促提示”给阿楠：提醒进入带练关键片段（不打断课程），如“现在是带练核心环节，深吸三次，专注画面5分钟，记录第1个闪念”。目的：强化现场获得感。",\n        "send_time": "21:05"\n      },\n      {\n        "content": "22:30（课后）立即私信收集红鞋子体验（F6升级）：问3个聚焦问题——1) 刚刚最明显的变化是什么？（专注/入睡/情绪/能量）2) 若要把它变成常态，你最想从哪一项开始？（可选项：每日仪式/时间管理脚本/额外引导）3) 是否愿意用一句话描述今晚的获得（方便社群分享）",\n        "send_time": "22:30"\n      },\n      {\n        "content": "18:45（尽快）完成并录入前两课的个性化反馈到CRM（F5升级）：记录其回放观看进度70%、能量测评（睡眠2/5、注意力3/5）、收藏工具清单偏好、凌晨活跃时段，以便上述私信内容高度个性化。",\n        "send_time": "18:45"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "F1",\n        "content": "18:30前私信提醒第三课重点与到课福利（更新为：强调今晚是“红鞋子飞跃”核心带练，附带夜猫子版一行‘专注脚本’示例，且提示带水和本子）"\n      },\n      {\n        "id": "F2",\n        "content": "引导准备水和本子，营造仪式感（更新为：给出三步仪式指引——1. 20:00前备水/本子/耳机 2. 20:00前5分钟关掉非必要通知 3. 进入教室后把今日目标写一行）"\n      },\n      {\n        "id": "F4",\n        "content": "帮其设置今晚“专注模式”手机脚本（升级为具体可复制脚本）：包含‘勿扰设置/计时器/手机桌面一句话状态’、10分钟预演步骤，并附‘夜猫子版微奖励’（完成10分钟给自己一杯水）。计划发送时间：19:50。"\n      },\n      {\n        "id": "F5",\n        "content": "整理前两课反馈并录入CRM（更新为：18:45完成，记录重点字段：观看进度70%、能量测评、收藏偏好、活跃时段、首要痛点“昼夜颠倒+拖延”）"\n      },\n      {\n        "id": "F6",\n        "content": "课后收集其“红鞋子飞跃”体验（更新为：22:30私信三问模板，并在收到反馈后在24小时内做个性化跟进建议）"\n      }\n    ],\n    "toRemove": [\n      "F3"\n    ]\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 7：家庭型决策，需要分期与伴侣支持（第四课后）'
        },
        'plan': '{\n  "think": "客户状态诊断：\\n- 成交阶段：承诺阶段（已完成四课，上课周全部参与），证据：客户为“四课全勤”。\\n- 关系/信任梯：信任偏向拥护（高参与度、持续反馈、群内分享），证据：第二课后反馈“阿尔法音频午休有效”；群内愿意分享经历且作业完成度高。\\n- 价值感知：已有明确获得感与Aha（知道能量跌落点在16:00；阿尔法音频午休有效；第四课“蓝鹰预演”画面感强并写下未来一周清单），说明她已经感知到课程能直接缓解午后崩溃，并能带来可操作的预演与日常工具。\\n- 核心痛点匹配：高度匹配——她的核心痛点为“午后崩溃、对孩子发火后自责”，课程内容（情绪减压、阿尔法音频、蓝鹰预演等）直接命中，证据：她的Aha（能量跌落点在16:00）和对阿尔法音频的积极反馈。\\n\\n核心成交阻力（一句话）：\\n- 财务/决策链：丈夫为财政把关且偏保守（预算2–3k），她需要一个“能让丈夫信服”的成本效益和家庭友好方案。\\n\\n今日策略目标（状态迁移）：\\n- 从“承诺/兴趣”迁移到“明确的购买意向并安排决策沟通”——目标是让Lydia在次日早晨（6:30–7:30可沟通）同意：要么直接选择分期方案并留位，要么确定并预约与丈夫的三方通话，形成可执行的下一步承诺。\\n\\n对话主线设计（微型故事）：\\n- 起：先庆祝并复盘四课的核心获得（强调已验证的工具：阿尔法音频能量补足；蓝鹰预演带来的清晰行动表；7天卡可立即体验）。引用她的Aha（16:00）。\\n- 承：把“现在能短期缓解”和“21天系统班能固化改变”连成因果，提出对她日常（尤其16:00）的具体改进路径（短练+卡片+每周预演），并把家庭场景（不影响照顾孩子、短时作业）写清楚，缓解丈夫的担忧。附上分期细则和每月成本对比，降低决策阻力。\\n- 转：提供“husband-ready”短讯模板和邀请三方通话选项，明确两个简单选择（即刻分期/预约三方），把决定窗口限定到明早（利用她可沟通时间6:30–7:30），制造温和紧迫感。\\n- 合：确认她的承诺（选项A或B），并约定后续激活步骤（激活7天卡、首次练习时间点：建议15:50用阿尔法/7天卡引导以应对16:00跌落）。\\n\\n今日已有任务评估（与对话主线的匹配分析）：\\n- G1 推送《7天冥想会员卡》激活提醒：匹配。应调整为“定位在缓解16:00跌落的使用场景+操作指引”，并在她可沟通时段发送。优先级高。\\n- G2 小窗梳理其“未来预演”脚本并确认时间点：匹配且关键（可把她第四课写下的未来一周清单转成具体时间点与行动）。优先级高。\\n- G3 邀请其在群里发一则体验（增强社证）：当前有冲突。她已高信任但现在首要是促成购买决策，公开分享可能分散她注意或在未确认购买前造成压力。建议延期到成交后作为成交后的社证任务或在获得明确承诺后再请其发声。\\n- G4 系统班邀约：强调家庭支持与作业轻量化：匹配，必须细化为“husband-ready”话术和家庭场景说明，凸显作业短时且可与带娃并行，降低丈夫异议。\\n- G5 提供3期/6期分期方案链接：匹配，必须同时给出具体月供数字并做对比（例如每月相当于某项日常开销），发送时间应在她能看手机的时间段。\\n- G6 如有需要，约与丈夫三方通话：匹配且关键。需在次日早晨提供具体可选时段（6:30–7:30或当天晚间）并给husband-ready文案供她事先发送。\\n- G7 复盘四课中最强的“获得感”，形成个性化承诺：匹配且必要，需把她的三大获得（阿尔法音频有效；16:00的自我觉察；蓝鹰的具体清单）浓缩成一句承诺并提出21天如何固化。\\n\\n结论：保留并优化大部分任务，删除/延后G3（群发体验）为成交后动作；重点新增“husband-ready模板”“明早预约消息/锁位流程”“具体分期明细与对比表”“7天卡针对16:00使用策略”。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "准备并在早上发送“husband-ready”两句模板（供Lydia直接转发给丈夫），并附上是否愿意三方通话的选项；模板强调家庭友好作业、分期月供与孩子受益点。",\n        "send_time": "06:40"\n      },\n      {\n        "content": "发送《7天冥想会员卡》激活引导（含步骤截图+建议使用时点：每日15:50作为16:00前短练，搭配阿尔法音频午休），并邀请她回个‘已激活’的简短确认。",\n        "send_time": "06:45"\n      },\n      {\n        "content": "发送清晰的分期+价格表（3期/6期每期金额、总价对比、对应每月成本与日常开销对比示例），并明确推荐方案与留位说明（如需立即锁位请回复‘锁位’）。",\n        "send_time": "06:50"\n      },\n      {\n        "content": "预约明早6:30–7:30的1v1/三方锁位通话（给出2个具体时间段供选，示例：06:40或07:10），并说明通话目的（快速为丈夫做说明/确认分期并锁位，10分钟）。",\n        "send_time": "06:55"\n      },\n      {\n        "content": "发送简短的‘复盘承诺’私聊稿：把四课最强获益浓缩成3句（引用其话：阿尔法音频午休有效；16:00能量跌落点明确；蓝鹰预演有具体清单）并用第一人称让她确认“我愿意在21天内把X变成习惯”。用于推动情感承诺。",\n        "send_time": "07:00"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "G1",\n        "content": "推送《7天冥想会员卡》激活提醒——优化为“操作引导+使用场景（15:50短练针对16:00跌落）+截图步骤+激活后请回复‘已激活’”。发送时间调整到早上6:45。"\n      },\n      {\n        "id": "G2",\n        "content": "小窗梳理其“未来预演”脚本并确认时间点——优化为“把你在第四课写下的周清单转成每日时间表（尤其16:00节点），确认每项具体执行时点并承诺第1天实践反馈时间”。建议对话安排在次日早晨沟通时段。"\n      },\n      {\n        "id": "G4",\n        "content": "系统班邀约：强调家庭支持与作业轻量化——优化为“husband-ready版邀约话术+针对带两个孩子的家庭如何安排每日练习（5–10分钟块状练习）+课程对妻子课堂能量的直接回报说明”。"\n      },\n      {\n        "id": "G5",\n        "content": "提供3期/6期分期方案链接——优化为“附带每期具体金额、总价与每月成本对比示例（用易懂生活场景比喻），并明确最推荐方案与如何立即锁位的指令按钮/回复词”。"\n      },\n      {\n        "id": "G6",\n        "content": "如有需要，约与丈夫三方通话——优化为“提出两个具体可选时间（次日06:40或07:10，或晚上20:00）并附husband-ready短讯模板，通话时长承诺≤10分钟，目标：快速说明价值并确认分期/锁位”。"\n      },\n      {\n        "id": "G7",\n        "content": "复盘四课中最强的“获得感”，形成个性化承诺——优化为“把三大获益（阿尔法音频效果；16:00识别点；蓝鹰预演形成的清单）写成一段‘你将实现的三点改变’，并请她选A（立即分期并锁位）或B（预约三方通话）作为下一步承诺）。"\n      }\n    ],\n    "toRemove": [\n      "G3"\n    ]\n  }\n}'
      },
      {
        'user': {
          'label': 'Case 8：已成交学员，进入入班运营与陪跑场景'
        },
        'plan': '{\n  "think": "第一步：客户状态诊断\\n- 成交阶段：成交（已报名21天系统班）。证据：客户画像显示“已付费，重视结构化陪跑”；关键记忆显示“第三课后即报名；第四课全勤”。\\n- 关系/信任梯：处于“熟悉→信任”阶段，但未完全上升到“拥护”。证据：Kris 全程沉默，但连续参与（第三课后报名、第四课全勤）并已开始落实练习（早晨《阿尔法音频》+晚上“秒睡”组合），表明对课程有信任感但互动偏低，尚未表现出主动拥护（如主动在群内分享、频繁反馈）。\\n- 价值感知：已有“获得感”但以工具化、个人体验为主——他已开始早晚组合练习，说明感知到课程带来的即时效果（睡眠改善与晨间能量），是以“情绪释放/习惯工具化”的获得感为主，而非强烈的未来憧憬。证据：已开始音频练习；目标明确是“将注意力块提升到90分钟”。\\n- 核心痛点匹配：课程价值与其痛点高度匹配：课程有沉浸式秒睡与专注力训练（红鞋子飞跃），直接对应“晚间刷论坛停不下来、注意力时好时坏、自律不稳定”。证据：课程设置（秒睡、阿尔法音频、红鞋子）已被Kris部分采用。\\n\\n核心成交阻力（一句话）：\\nKris 对频繁、无结构的互动敏感且习惯沉默——若跟进方式是“问候式/频繁打扰”，将阻碍他进一步深度参与与成为拥护者。\\n\\n今日策略目标（状态迁移）：\\n把Kris 从“信任但被动执行”迁移到“主动用结构化工具践行并确认学伴意愿”的状态：具体是让他在当天明确接受可一键导入的21天日历/待办模板并选择是否允许学伴匹配（低摩擦的‘接受/拒绝’二选项），从而把信任转化为可追踪的行为承诺。\\n\\n对话主线设计（微型故事）：\\n“工程化的陪跑方案”——开场用事实确认（你已开始早晚练习/第三、第四课参与），直接给出工程师式的可导入产出（.ics 日历、一键待办模板、闹钟/番茄段落、夜间‘防火墙’清单），并以极简二选（‘接受学伴/不接受学伴’）让他作出低成本决策；结尾给予可选、无压力的曝光（30秒感言改为完全自愿、见效后邀请）。微故事节奏：确认成绩→交付结构化工具→提出低摩擦选择→给予空间（并说明接下来的自动化支持）。\\n\\n第二步：对今日已有任务列表评估\\n原任务列表：H1 发送欢迎礼包；H2 制定个性化21天计划+闹钟模板；H3 匹配作息相近学伴；H4 第3天回访确认练习打卡；H5 邀请自愿录制30秒新生感言。\\n\\n- 与对话主线匹配的任务：\\n  - H1（欢迎礼包）高度匹配——但需升级为“包含一键导入的日历/待办包”和“90分钟专注模块+夜间防火墙清单”，并在消息结尾做单一二选行动（是否同意学伴匹配）。这是对话主线的核心交付物。证据：Kris 重视结构化、偏好一键导入（使用待办App与日历）。\\n  - H2（个性化21天计划+闹钟模板）高度匹配——要以可导入文件与短说明为主，避免长篇文本，使其可直接放进工具链（待办App、日历）。\\n  - H4（第3天回访）匹配，但应被设为“触发式”的回访（自动化/模板化），并安排在系统班第3天的早上7:50，以符合Kris 的在线时段。\\n\\n- 需要调整/避免的任务：\\n  - H3（匹配学伴）：原则上匹配有价值，但必须变为“学员明确同意后才匹配”，且匹配逻辑要优先考虑“早晨7:45–8:30在线、偏好结构化打卡”的人，且匹配后的沟通必须是极简卡片（例如30字自我描述+1条日常约定），以免触发他对频繁社交的排斥。\\n  - H5（邀请录制30秒感言）：当前为高风险/冗余任务。Kris 不喜欢被频繁私聊且全程沉默，强行邀请会适得其反。应移除或推迟为“见效后自愿激励机制”（例如完成7天打卡给出可选邀请），而不是当前立即执行。\\n\\n- 缺失的任务（必须补上以服务对话主线）：\\n  1) 在今天的早间发送一次私聊：把“欢迎礼包 + 一键导入包 + 90分钟专注模板 + 夜间防火墙”合并成一条极简卡片，并在末尾给出明确的二选按钮（‘接受导入并匹配学伴’ / ‘只要工具包’）。这是把信任转为行为承诺的关键触点，需在Kris 仍能看到的时间段发送（当天尽早）。证据：Kris 早上7:45–8:30在线，但当前时间为10:00，仍可在10:15发送一条简洁可操作的消息；他不喜欢频繁问候式私聊，但会响应“明确清单与节奏”。\\n  2) 发送夜间‘防火墙’行动包（替代行为、屏蔽工具、睡前流程），时间可安排在当晚18:30 发给他，目的是在晚间诱发刷论坛前提供替代策略，降低晚间破坏性行为。证据：核心痛点是“晚间刷论坛停不下来”。\\n  3) 预先准备由系统自动触发的学伴匹配候选卡（仅在他回复‘接受匹配’后发送），以及一份可复制的短介绍模板（30字），以保证匹配沟通低摩擦。\\n\\n綜合结论：保留 H1、H2、H4，但要升级内容与交付形式；把 H3 改为“经客户同意后的被动/低频匹配流程”；移除 H5 并在后续转为“见效后自愿邀请”。并新增至少两条今天必须执行的低摩擦任务：① 私聊一次合并卡片（工具包+二选），② 今晚发送夜间防火墙行动包（替代行为+App建议+睡前仪式）。",\n  "plans": {\n    "toAdd": [\n      {\n        "content": "私聊：发送极简欢迎卡片（合并版）——包含：①一键导入日历(.ics)与待办App导入包；②第一周的90分钟专注训练模板（含番茄分段与闹钟说明）；③夜间‘防火墙’清单摘要（3步替代行为）；并在卡片末尾给出二选行动：回复“接受匹配”或“只要工具包”。（目的：把信任转为可追踪的行为承诺）",\n        "send_time": "10:15"\n      },\n      {\n        "content": "发送夜间戒网“防火墙”行动包（私聊/群视情况）——包含：①替代行为清单（秒睡+阅读+手机放置位置）；②推荐屏蔽App与自动化小技巧；③睡前仪式模板（3步）；并注明‘若今晚想试，我可以帮你把闹钟与待办一次性导入’（低压激励）。",\n        "send_time": "18:30"\n      },\n      {\n        "content": "准备学伴候选卡（模板化、30字自我介绍）并设为条件任务：若Kris 在私聊回复“接受匹配”，则在收到同意后当天11:00 发送学伴候选卡；（若不接受则不发送）。",\n        "send_time": "11:00"\n      }\n    ],\n    "toUpdate": [\n      {\n        "id": "H1",\n        "content": "发送欢迎礼包：课表/学习手册/群规，并直接附带一键导入包（.ics 日历 + 待办App导入模板）、《冥想练习指南》与90分钟专注模块速览；消息结尾放置二选项（接受匹配/只要工具包），建议发送时间：10:15。"\n      },\n      {\n        "id": "H2",\n        "content": "制定个性化21天计划+闹钟模板（输出为可导入文件与简短说明），含：周一至周日具体练习条目、专注时段分段、晨间/晚间闹钟样板；在消息中明确如何一键导入到Kris常用的待办App与日历。"\n      },\n      {\n        "id": "H3",\n        "content": "匹配作息相近的学伴（仅在学员明确同意后执行），匹配优先条件：早上在线7:45–8:30、偏好结构化打卡；匹配后发送30字自我介绍卡片与日常约定（低摩擦、非打扰式）。"\n      },\n      {\n        "id": "H4",\n        "content": "第3天回访确认练习打卡情况：提前准备可复制回访模板（两句话+三项核对点），回访触发时间：系统班第3天早上07:50（与Kris的在线窗口对齐）。"\n      }\n    ],\n    "toRemove": [\n      "H5"\n    ]\n  }\n}'
      }
    ]

    const users = [
      // Case 1｜预热期：理性型失眠人群（小讲堂当晚）
      {
        user_slots: `
- 昵称：小叶（F/28，北京）
- 职业：互联网产品经理（并行项目多）
- 家庭：单身，独居
- 收入：年薪约35万
- 作息/设备：常23:30后入睡；夜醒2–3次；iPhone；微信活跃21:30–00:30
- 目标：7天把入睡时长压到≤15分钟；21天形成稳定晚间仪式
- 核心痛点：入睡困难、脑压高、晨起乏力
- 购买偏好：理性决策，要证据与复利逻辑；预算2–3k，可分期
- 抗拒点：担心“玄学”、怕浪费时间；不爱群内曝光
- 关键词：科学、可操作、效率、数据可见
`,
        memory: `
- 参与小讲堂并做海浪减压8分钟，反馈“头部压力从3降到1，有哈欠”
- 能量测评：情绪电量48/100；睡眠质量2/5；专注度3/5；焦虑7/10
- 过往：用睡眠App打卡14天后放弃；喜欢番茄钟
- 已领取并收藏《冥想练习指南》
- 私聊提问：长期失眠是否影响记忆力？
`,
        user_behavior: `
- 小讲堂准时到课（停留87分钟），提交测评表单
- 群内发言2次；私信1次；22:48完成3分钟呼吸练习打卡
- 互动时段集中在22:30–23:50
`,
        silent_analyze: `
- 18:00–21:00常沉默（通勤/晚饭），不适合长信息
- 对“科学依据/数据对照”类内容回复率高；对“价格/报名通道”暂未回应
- 建议用要点+单图格式，便于快速消化
`,
        current_time: '2025-08-16 19:10',
        stage: '认知（小讲堂当晚）',
        today_event: '小讲堂（上课周前）+能量测评引导',
        tomorrow_event: '开营仪式（周日 20:00 社群）',
        existing_task: `[
      {"id":"A1","content":"提醒今晚小讲堂，并预告海浪减压"},
      {"id":"A2","content":"发送《冥想练习指南》并标注入门要点"},
      {"id":"A3","content":"发送能量测评表单）"},
      {"id":"A4","content":"私信个性化测评反馈"},
      {"id":"A7","content":"发布明日20:00开营仪式海报与入群指引"}
    ]`
      },

      // Case 2｜开营仪式后：ROI导向的创业者
      {
        user_slots: `
- 昵称：阿城（M/35，上海）
- 职业：ToB创业者（轻资产服务，现金流压力）
- 家庭：已婚一娃
- 收入：波动大（15–60万/年）
- 目标：缓解财务焦虑，提高决策清晰度
- 核心痛点：入睡易醒、白天心悸；对“回报”高度敏感
- 购买偏好：看案例与ROI；预算3–5k，可月付
- 抗拒点：担心耗时、与业务无关的“心灵课程”
`,
        memory: `
- 错过小讲堂，回放看了17分钟（倍速1.25）
- 开营仪式期间在群里问：“课程对赚钱有直接帮助吗？”
- 能量测评：压力8/10；注意力3/5；睡眠3/5
- 曾学过“效率手册”类课程，能坚持2周
`,
        user_behavior: `
- 20:07进入开营直播，停留49分钟；收藏1张“课程路径图”
- 深夜23:40–00:20回复较快；白天响应慢
- 提问集中在“商业应用/ROI/案例”
`,
        silent_analyze: `
- 14:00–19:30在客户拜访，基本不看消息
- 若信息>5行，阅读率下降；偏好时间戳式要点
- 对“成功学口吻”反感，需克制营销语
`,
        current_time: '2025-08-17 21:00',
        stage: '认知→体验（开营仪式后）',
        today_event: '开营仪式（上课周前）',
        tomorrow_event: '第一课（周一 20:00 情绪减压）',
        existing_task: `[
      {"id":"B1","content":"补发小讲堂回放"},
      {"id":"B2","content":"邀请在群里分享“财富卡点”1句话"},
      {"id":"B3","content":"今晚22:00设置第一课开播提醒"},
      {"id":"B5","content":"私聊引导完成能量测评并回传截图"},
      {"id":"B6","content":"准备“ROI常见疑问”简版FAQ卡片"}
    ]`
      },

      // Case 3｜第一课后：新手妈妈（睡浅、时间碎片）
      {
        user_slots: `
- 昵称：安安（F/31，深圳）
- 身份：休产假进入尾声，新手妈妈
- 目标：改善浅眠与肩颈紧张；不打扰宝宝作息
- 核心痛点：入睡慢、夜醒后难再睡；时间切片化
- 购买偏好：短时高效音频；预算1.5–2k，需分期
- 抗拒点：担心无法连续学习
`,
        memory: `
- 第一课全程；沉浸式秒睡练习10分钟时感到“肩颈热、放松”
- Aha：看到“身心对照表”能自查症状对应练习
- 能量测评：睡眠1/5；情绪电量55/100；专注3/5
- 提及：宝宝夜奶时间大概02:30/05:30
`,
        user_behavior: `
- 课中发送“打哈欠”的表情2次；课后在群里点了“有帮助”
- 晚上23:10以后未读消息（可能忙哄娃）
- 次日10:20补看讲义3页
`,
        silent_analyze: `
- 22:30后易中断；上午10:00–12:00较空
- 喜欢“带练语音+清单式步骤”，不爱长文
- 对“时间友好型方案”敏感
`,
        current_time: '2025-08-18 22:05',
        stage: '体验（第一课后）',
        today_event: '第一课结束；赠《身心对照表》',
        tomorrow_event: '第二课（周二 20:00 财富唤醒）',
        existing_task: `[
      {"id":"C1","content":"课后发送《身心对照表》并标注自查方法"},
      {"id":"C2","content":"私聊追问“秒睡体验”并收集1句可复用见证"},
      {"id":"C3","content":"预约明天课前10分钟复训链接"},
      {"id":"C4","content":"白天群内硬广21天课程（不合规，应删）"},
      {"id":"C5","content":"了解家人支持度与可学习时段"},
      {"id":"C6","content":"在CRM记录标签【时间碎片】【年轻妈妈】"}
    ]`
      },

      // Case 4｜第二课后：学生党（注意力分散、预算敏感）
      {
        user_slots: `
- 昵称：七七（M/24，杭州）
- 身份：考研二战生
- 目标：提升专注与复盘效率
- 核心痛点：拖延、刷短视频、坐不住
- 购买偏好：奖学金/学生价；预算≤1.2k
- 决策链：父母有强影响力
`,
        memory: `
- 第二课全程；“财富果园”画面：土壤湿润但果实稀疏（自评“行动少”）
- Aha：意识到“躲避难题→短期快感”的循环
- 能量测评：注意力2/5；情绪电量62/100；睡眠3/5
`,
        user_behavior: `
- 课后主动交了“果园描述”，点赞6次
- 常在下午16:00–18:00在线；深夜回复慢
`,
        silent_analyze: `
- 对“同龄人案例”“学习力提升曲线”感兴趣
- 对价格非常敏感，比较沉默
`,
        current_time: '2025-08-19 22:55',
        stage: '体验（第二课后）',
        today_event: '第二课结束；赠《阿尔法音频》',
        tomorrow_event: '第三课（周三 20:00 红鞋子飞跃—核心课）',
        existing_task: `[
      {"id":"D1","content":"一对一文字解读他的“财富果园”画面"},
      {"id":"D2","content":"发送《阿尔法音频》并指导午休听10分钟"},
      {"id":"D3","content":"第三课提醒+价值预告（专注力飞跃）"},
      {"id":"D4","content":"预留系统班奖学金位（准备但不宣传）"},
      {"id":"D5","content":"课前试探价格敏感度（不直接报价）"},
      {"id":"D6","content":"群内邀请其简短分享画面感受"}
    ]`
      },

      // Case 5｜第三课后（可销售期）：高意向的外企中层
      {
        user_slots: `
- 昵称：Sean（M/42，广州）
- 职业：外企中层，带8人团队
- 目标：突破“低能量高责任”的瓶颈
- 核心痛点：疲惫与焦虑并存，周末也难休息
- 购买偏好：重视系统性、服务承诺；预算充足
- 抗拒点：担心自己“坚持不下去”
`,
        memory: `
- 第三课后21:30私信：“红鞋子飞跃对我很有用”
- Aha：找到了“能量-专注-行动”闭环
- 过往：报过教练课（3个月），中途掉线
- 想要：有人监督的行动样例
`,
        user_behavior: `
- 四课基本全勤趋势；群内发言3次，影响力较高
- 资料查看及时（5分钟内）
`,
        silent_analyze: `
- 抗拒点在“能否坚持”，需提供结构化陪跑与复盘机制
- 晚上22:00后不看消息，第二天9:30前能回复
`,
        current_time: '2025-08-20 22:30',
        stage: '承诺倾向（第三课后，可销售期）',
        today_event: '第三课结束（21:00后开启销售）',
        tomorrow_event: '第四课（周四 20:00 高阶加播：蓝鹰预演）',
        existing_task: `[
      {"id":"E1","content":"21:40小窗邀约1v1问答，澄清目标与卡点"},
      {"id":"E2","content":"基于第三课练习生成“7天行动样例”"},
      {"id":"E3","content":"发送系统班介绍长图+学员案例（可销售期）"},
      {"id":"E4","content":"提供两种支付方案与退费规则说明"},
      {"id":"E5","content":"预约周四课后跟进"},
      {"id":"E6","content":"统计其群内发言作为社证素材"}
    ]`
      },

      // Case 6｜第三课前（禁止直销）：昼夜颠倒的自由职业者
      {
        user_slots: `
- 昵称：阿楠（M/29，成都）
- 身份：自由职业视觉设计
- 目标：把作息拉回正常，建立交付节律
- 核心痛点：拖延+昼夜颠倒；对“仪式感”有好奇
- 购买偏好：看“时间管理脚本”；预算2k左右
`,
        memory: `
- 第一、二课回放已看70%；常凌晨互动
- 朋友转介绍来的（对导师有初始信任）
- 能量测评：睡眠2/5；注意力3/5；情绪电量50/100
`,
        user_behavior: `
- 常在00:30–02:00活跃；白天不稳定
- 会收藏“工具清单”类内容
`,
        silent_analyze: `
- 对“具体操作脚本”反应最好；对“理念讲解”耐心较低
`,
        current_time: '2025-08-20 18:20',
        stage: '体验（第三课前，禁止直销）',
        today_event: '第三课（20:00–22:20，核心课）',
        tomorrow_event: '第四课预告/第三课回放与答疑',
        existing_task: `[
      {"id":"F1","content":"18:30前私信提醒第三课重点与到课福利"},
      {"id":"F2","content":"引导准备水和本子，营造仪式感"},
      {"id":"F3","content":"先发系统班价格（不合规，应删）"},
      {"id":"F4","content":"帮其设置今晚“专注模式”手机脚本"},
      {"id":"F5","content":"整理前两课反馈并录入CRM"},
      {"id":"F6","content":"课后收集其“红鞋子飞跃”体验"}
    ]`
      },

      // Case 7｜第四课后：家庭型决策者（需分期&伴侣支持）
      {
        user_slots: `
- 昵称：Lydia（F/38，南京）
- 职业：小学教师，带两娃
- 目标：修复情绪耗竭，提高下午课堂能量
- 核心痛点：午后崩溃、对孩子发火后自责
- 购买偏好：分期/家庭友好作业；预算2–3k
- 决策链：丈夫为财政把关人，偏保守
`,
        memory: `
- 四课全勤；第二课后反馈“阿尔法音频午休有效”
- 第四课“蓝鹰预演”画面感强，写下未来一周清单
- Aha：意识到“能量跌落点在16:00”
`,
        user_behavior: `
- 群内愿意分享经历；作业完成度高
- 晚上21:30后不看手机；早上6:30–7:30可沟通
`,
        silent_analyze: `
活跃度较高，经常分享家庭生活
`,
        current_time: '2025-08-21 21:50',
        stage: '承诺（第四课后）',
        today_event: '第四课结束；赠《7天冥想会员卡》',
        tomorrow_event: '1v1定制咨询/锁位沟通',
        existing_task: `[
      {"id":"G1","content":"推送《7天冥想会员卡》激活提醒"},
      {"id":"G2","content":"小窗梳理其“未来预演”脚本并确认时间点"},
      {"id":"G3","content":"邀请其在群里发一则体验（增强社证）"},
      {"id":"G4","content":"系统班邀约：强调家庭支持与作业轻量化"},
      {"id":"G5","content":"提供3期/6期分期方案链接"},
      {"id":"G6","content":"如有需要，约与丈夫三方通话"},
      {"id":"G7","content":"复盘四课中最强的“获得感”，形成个性化承诺"}
    ]`
      },

      // Case 8｜已成交：程序员新学员的入班运营
      {
        user_slots: `
- 昵称：Kris（M/30，武汉）
- 职业：程序员，独居，轻度社交回避
- 目标：21天把注意力块提升到90分钟
- 核心痛点：晚间刷论坛停不下来；自律时好时坏
- 购买偏好：已付费，重视结构化陪跑
`,
        memory: `
- 第三课后即报名；第四课全勤
- 已开始早晨《阿尔法音频》+晚上“秒睡”组合
- 期待：学伴匹配+可视化打卡
`,
        user_behavior: `
- 00:00后基本不在线；早上7:45–8:30在线
- 热衷工具：待办清单App、日历
`,
        silent_analyze: `
- 不喜欢频繁“问候式”私聊；更喜欢明确清单与节奏
- 全程沉默
`,
        current_time: '2025-08-22 10:00',
        stage: '成交（已报名21天系统班）',
        today_event: '入班欢迎+个性化学习计划制定',
        tomorrow_event: '系统班第2天：巩固与回访',
        existing_task: `[
      {"id":"H1","content":"发送欢迎礼包：课表/学习手册/群规"},
      {"id":"H2","content":"制定个性化21天计划+闹钟模板"},
      {"id":"H3","content":"匹配作息相近的学伴"},
      {"id":"H4","content":"第3天回访确认练习打卡情况"},
      {"id":"H5","content":"邀请自愿录制30秒新生感言"}
    ]`
      }
    ]

    let i = 0

    for (const jsonElement of json) {
      jsonElement.plan = JSON.parse(jsonElement.plan)
      jsonElement.user.detail = users[i]
      jsonElement.user.detail.existing_task = JSON.parse(jsonElement.user.detail.existing_task)

      console.log(i)
      i++
    }

    console.log(JSON.stringify(json, null, 4))
  }, 30000)
})