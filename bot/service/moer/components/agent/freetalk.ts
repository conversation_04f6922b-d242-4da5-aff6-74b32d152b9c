import { <PERSON>rWorkFlowNode, trackInvoke } from '../flow/nodes/baseNode'
import { IWorkflowState } from '../flow/flow'
import { MoerNode } from '../flow/nodes/type'
import { LLMNode } from '../flow/nodes/llm'
import { ChatStateStore } from '../../storage/chat_state_store'
import { FreeThink } from './freethink'
import { PostSaleNode } from '../flow/nodes/postSaleNode'
import { MetaActionRouter } from '../meta_action/meta_action_router'
import { Planner } from '../planner'
import { TaskManager } from '../planner/task/task_manager'

export class FreeTalk extends MoerWorkFlowNode {

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state.is_complete_payment && !ChatStateStore.get(state.chat_id).state.is_complete_post_sale) {
      return await PostSaleNode.invoke(state)
    }

    const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(state.chat_id, state.round_id)

    const { action, strategy, task } = await FreeThink.invoke(state, metaActionStage)

    await Planner.addPassiveTasks(state.chat_id, task, state.round_id)
    const actionInfo = await MetaActionRouter.handleAction(state.chat_id, state.round_id, action)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      metaActionStage.guidance,
      actionInfo.guidance
    ].filter(Boolean).join('\n')

    await LLMNode.invoke({
      state,
      model: 'gpt-4.1',
      useRAG: true,
      recallMemory: true,
      chatHistoryRounds: 6,
      promptName: 'free_talk',
      noStagePrompt: true,
      dynamicPrompt: talkStrategyPrompt,
      postReplyCallBack: actionInfo.callback
    })


    TaskManager.checkAndMarkTasksCompleted(state.chat_id, state.round_id)

    return MoerNode.FreeTalk
  }
}