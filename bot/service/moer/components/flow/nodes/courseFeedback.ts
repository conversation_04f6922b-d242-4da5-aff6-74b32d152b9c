import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { LLMNode } from './llm'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { FreeTalk } from '../../agent/freetalk'
import { DataService } from '../../../getter/getData'
import { ContextBuilder } from '../../agent/context'
import { MessageSender } from '../../message/message_send'


export class CourseFeedbackDay1 extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    //     // 只做一次作业打卡解读
    //     if (ChatStateStore.getFlags(state.chat_id).is_complete_day1_homework_feedback) {
    //       return await FreeTalk.invoke(state)
    //     }
    //
    //     await LLMNode.invoke({
    //       state,
    //       dynamicPrompt: `客户在课后主动提交作业，请针对性处理客户困惑。鼓励后面2天的持续学习。
    // 建议的回答思路是：告知客户刚开始学习有各种问题（例如有杂念，进入不了画像，安静不下来等等）都是的非常正常的；肯定同学意识到问题就是一种进步。最后给出下次再遇到这类情况可以尝试的处理方式是什么，引导耐心等待自己的进步。
    // 例如：客户打卡回复头脑里有杂念。
    // 参考回答：嗯嗯，这个在刚开始得时候很正常，正是因为平时我们的都被各样的杂念缠绕，所以我们才需要冥想来排查杂念，看到真我。而且看到真我，已经有是一种进步了！下次当这个引起焦虑的念头出现时，不要急着抗拒它。可以试着把这个念头当作一片飘过的云彩，只是静静地看着它，不评判，不追随。当你能以这样平和的心态去面对它时，您会发现它的影响力会逐渐减弱。反而，你越在意他，越容易被念头抓住。这个需要一个练习得过程哈，越来越快速看见杂念，越来越快放下杂念，归于平静。跟着老师慢慢来`,
    //       useRAG: true,
    //       recallMemory: true,
    //       noStagePrompt: true,
    //     })
    //
    //     ChatStateStore.update(state.chat_id, {
    //       state: {
    //         is_complete_day1_homework_feedback: true
    //       }
    //     })

    return await FreeTalk.invoke(state)
  }
}


export class CourseFeedbackDay2 extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    //     await LLMNode.invoke({
    //       state,
    //       dynamicPrompt: `客户在课后主动提交作业，你要让他们感受到努力被看见，问题被引导，让他们对继续学习课程充满期待。对第三节老师最重磅的红靴子给予足够重视。你可以尝试用以下思路来应对客户：
    // - 积极肯定客户在课堂中得到的收获，如果客户有什么疑惑，先告知这是刚入门都会遇到的问题，消除他们的担心和疑惑，说明这类问题的处理方式和态度是什么即可
    // - 如果客户遇到的是生活中的问题，例如工作不顺利，家庭问题，个人发展卡点等问题，都可以用一个逻辑来回答，可以帮助客户拆解这些问题，拆解后都会发现，这些都是内心问题，可以通过觉察来提升对这些问题的解决能力`,
    //       useRAG: true,
    //       recallMemory: true,
    //       noStagePrompt: true,
    //     })

    return await FreeTalk.invoke(state)
  }
}


export class CourseFeedBackDay3NotInClass extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    //     await LLMNode.invoke({
    //       state,
    //       dynamicPrompt: `解答客户问题，重点引导客户关注蓝鹰预演，可以结合案例说明蓝鹰预演的价值。
    // 例如：好的，等我消息哈，蓝鹰预演是导师班压轴课哦，能大大帮助我们提升目标实现的可能性`,
    //       useRAG: true,
    //       recallMemory: true,
    //       noStagePrompt: true,
    //     })
    //
    //     return MoerNode.FreeTalk

    return await FreeTalk.invoke(state)
  }
}


export class CourseFeedBackDay3InClass extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const userSlots = ChatStateStore.get(state.chat_id).userSlots
    const nodeInvokeCount = ChatStateStore.getNodeCount(state.chat_id, CourseFeedBackDay3InClass.name)
    if (nodeInvokeCount === 0) {
      let energyTestResult =  ''
      if (userSlots.energy_test_score !== undefined) {
        if (userSlots.energy_test_score < 200) {
          energyTestResult = `客户之前做的能量测评分数为 ${userSlots.energy_test_score}，可能会有一些负面消极的情绪`
        } else if (userSlots.energy_test_score >= 200 && userSlots.energy_test_score <= 300) {
          energyTestResult = `客户之前做的能量测评分数为 ${userSlots.energy_test_score} 属于中间能量的范围，生活态度通常是积极的，还有一定的提升空间`
        } else {
          energyTestResult = `客户之前做的能量测评分数为 ${userSlots.energy_test_score} 属于正能量的范围，已经很不错了`
        }
      }

      const customerPortrait = await ContextBuilder.getCustomerPortrait(state.chat_id)
      if (energyTestResult && customerPortrait) {
        await LLMNode.invoke({
          state,
          customPrompt: `## 课后回访
参考客户过去的痛点，经历和能量测评结果，询问客户的练习感受

## 客户画像
${customerPortrait}

能量测评结果:
"${energyTestResult}"

红靴子冥想核心解决的帮助是:红靴子主要帮助提升觉知，提升专注力和能量的

结合客户痛点，询问客户练习感受。
例如：红靴子咱们练得感觉怎么样呀？看到咱们之前沟通过xxx，红靴子对咱们内心力量提升应该很有帮助~

以 "红靴子咱们练得感觉怎么样呀？" 开始回复`,
          chatHistoryRounds: 0,
          noStagePrompt: true,
          noSplit: true,
          noUserSlots: true,
        })
      } else {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '红靴子咱们练得感觉怎么样呀？红靴子的练习正是为了帮助提升觉知力、专注力以及能量的，不知道在这些方面，您有没有感受到一些变化或者帮助呢？'
        })
      }
    } else {
      return await FreeTalk.invoke(state)
    }


    return MoerNode.FreeTalk
  }
}
